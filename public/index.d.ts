import { AdsState, AdsSymbolContainer } from "ads-client";
export interface L33tTimed {
    timedLast: number;
    timedPrevious: number;
    timedTTL: number;
}
export interface L33tTimedInfo {
    timestamp: string;
}
export declare class L33tTask implements L33tTimed {
    timedLast: number;
    timedPrevious: number;
    timedTTL: number;
    _nodeinterval: NodeJS.Timeout | null;
    path: string | null;
    source: string | undefined;
    scheduledInterval: number;
    lastResult?: string;
    fileContent: string;
    autoStart: boolean;
    error: string | null;
    isStarted: boolean;
    toJSON(): {
        timedTTL: number;
        timedLast: number;
        timedPrevious: number;
        path: string | null;
        scheduledInterval: number;
        fileContent: string;
        lastResult: string | undefined;
        error: string | null;
    };
}
export declare class L33tConfig {
    zoom?: number | undefined;
    rotation_angle?: number | undefined;
    crop_x?: number | undefined;
    crop_y?: number | undefined;
    crop_width?: number | undefined;
    crop_height?: number | undefined;
    constructor(zoom?: number | undefined, rotation_angle?: number | undefined, crop_x?: number | undefined, crop_y?: number | undefined, crop_width?: number | undefined, crop_height?: number | undefined);
}
export declare class L33tMessage {
    iteration?: number | undefined;
    image?: string | undefined;
    time: number;
    memory_usage?: number | undefined;
    detections?: Array<{
        class: string;
        confidence: number;
        bbox: number[];
    }> | undefined;
    constructor(iteration?: number | undefined, image?: string | undefined, time?: number, memory_usage?: number | undefined, detections?: Array<{
        class: string;
        confidence: number;
        bbox: number[];
    }> | undefined);
}
export type Point = {
    x: number;
    y: number;
};
export type ShapeType = 'polygon' | 'line' | 'point';
export interface DrawingCanvasProps {
    backgroundImage: string;
    initialShapes: Record<string,Shape>;
    onSave: (shapes:  Record<string,Shape>) => void;
}
export interface Shape {
    id: string;
    type: ShapeType;
    points: Point[];
    isClosed?: boolean;
}
export declare class L33tAgent implements L33tTimed {
    name:string;
    lastMessage?: L33tMessage | undefined;
    config?: L33tConfig | undefined;
    shapes: Shape[];
    timedLast: number;
    
    timedPrevious: number;
    timedTTL: number;
    constructor(lastMessage?: L33tMessage | undefined, config?: L33tConfig | undefined, shapes?: Shape[]);
    toJSON(): {
        lastMessage: L33tMessage | undefined;
        config: L33tConfig | undefined;
        shapes: Shape[];
        timedLast: number;
        timedPrevious: number;
        timedTTL: number;
    };
}
export type L33tMemDB = {
    agents: Record<string, L33tAgent>;
    tasks: {
        [id: string]: L33tTask;
    };
    plcs: Record<string, L33tADSPLC>;
};
export interface L33tADSPLC extends L33tTimed {
    connected: boolean;
    description: string;
    ip: string;
    driver: string;
    timedLast: number;
    timedPrevious: number;
    timedTTL: number;
    symbols: AdsSymbolContainer | undefined;
    boundVariables: string[];
    values: {
        [key: string]: any;
    };
    localAmsNetId: string;
    localAdsPort: number;
    targetAmsNetId: string;
    targetAdsPort: number;
    routerAddress: string;
    routerTcpPort: number;
    status?: AdsState;
    error: unknown;
    setHandler<T>(h: T): void;
    updateBoundVariables(varKeys: string[]): Promise<void>;
    addBoundVariables(varKeys: string[]): Promise<void>;
    writeValue(name: string, value: any): boolean;
    toJSON(): {
        description: string;
        ip: string;
        driver: string;
        timedLast: number;
        timedPrevious: number;
        timedTTL: number;
        symbols: AdsSymbolContainer | undefined;
        boundVariables: string[];
        values: {
            [key: string]: any;
        };
        connected: boolean;
        status?: AdsState;
        error: unknown;
    };
}
export declare class L33tADSPLCImpl implements L33tADSPLC {
    connected: boolean;
    description: string;
    ip: string;
    driver: string;
    timedLast: number;
    timedPrevious: number;
    timedTTL: number;
    symbols: AdsSymbolContainer | undefined;
    boundVariables: string[];
    values: {
        [key: string]: any;
    };
    localAmsNetId: string;
    localAdsPort: number;
    targetAmsNetId: string;
    targetAdsPort: number;
    routerAddress: string;
    routerTcpPort: number;
    _handler: any;
    status?: AdsState;
    error: unknown;
    setHandler<AdsSockeantHandler>(h: AdsSockeantHandler): void;
    updateBoundVariables(varKeys: string[]): Promise<void>;
    addBoundVariables(varKeys: string[]): Promise<void>;
    writeValue(name: string, value: any): boolean;
    toJSON(): {
        description: string;
        ip: string;
        driver: string;
        timedLast: number;
        timedPrevious: number;
        timedTTL: number;
        symbols: AdsSymbolContainer | undefined;
        boundVariables: string[];
        values: {
            [key: string]: any;
        };
        connected: boolean;
        status: AdsState | undefined;
        error: unknown;
    };
}
