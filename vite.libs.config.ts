// vite.models.config.js
import { defineConfig } from "vite";
import dts from "vite-plugin-dts";

export default defineConfig({
  build: {
    lib: {
      entry: 'app/model/index.ts',
      formats: ['es'],
      fileName: 'l33t-lib'
    },
    outDir: 'build/l33t-lib',
    emptyOutDir: true,
    copyPublicDir: false,  // Questo impedisce la copia della cartella public
  },
  plugins: [
    dts({
      include: ['app/model/**/*.ts'], // Include tutti i file .ts nella cartella model
      exclude: ['app/model/**/*.spec.ts', 'app/model/**/*.test.ts'], // Esclude i file di test
      rollupTypes: true, // Combina tutti i tipi in un unico file
    })
  ],
});


