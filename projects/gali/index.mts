/*
    TODO - Examples
    Read bounded variables of PLC
    let variables = db.plcs['192.168.30.180'].boundVariables
*/

import { L33tADSPLC, L33tMemDB } from "l33t-lib"
// import { L33tMemDB } from "~/app/model/l33t-memdb.js"

var PLCH:L33tADSPLC
export async function init(db:L33tMemDB) {

    PLCH = db.plcs['HUNDEGGER']
    
    await PLCH.addBoundVariables([
            ".c1_Ftc1_Fila1_Presenza",
            ".TP_BLINK_1SEC_GLV",
            "RULLI_CENTRO_TAGLI.RCT_Semaforo.analog1"
        ])
}


// Export the run function that will be called by the task manager
// Your task logic here
export async function run(db:L33tMemDB) {
    
    let varOne = PLCH.values['.C1_Ftc1_Fila1_Presenza']
    let blink = PLCH.values['.TP_BLINK_1SEC_GLV']
    let s1 = PLCH.values[".RTC_Semaforo.analog1"]
    
    if (varOne == false) {
        return "false" + s1 + " - " + blink
    } else 
        return "trueee" + s1 + " - " + blink

    //return  variables + ": " + varOne +  "<br><br>" + Date.now().toString()
}
