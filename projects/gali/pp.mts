/*
    TODO - Examples
    Read bounded variables of PLC
    let variables = db.plcs['192.168.30.180'].boundVariables
*/
import { L33tADSPLC, L33tMemDB } from "l33t-lib";
import * as THREE from "three"
import { L33tGeometryUtil } from "~/server/L33tGeometryUtil";

var PLCH:L33tADSPLC



function pointsToShape(points: [number, number][]): THREE.Shape {
    // Create a new Shape
    const shape = new THREE.Shape();
    
    // Start from the first point
    if (points.length > 0) {
        const [startX, startY] = points[0];
        shape.moveTo(startX, startY);
        
        // Draw lines to subsequent points
        for (let i = 1; i < points.length; i++) {
            const [x, y] = points[i];
            shape.lineTo(x, y);
        }
        
        // Close the shape by connecting back to the first point
        shape.closePath();
    }
    
    return shape;
}


function pointsToVectors(points: [number, number][]): THREE.Vector3[] {
    return points.map(([x, y]) => new THREE.Vector3(x, y, 0));
}


function bboxToVectors(bbox: number[]): THREE.Vector3[] {
    const [x, y, w, h] = bbox;
    
    // Create vectors for all 4 corners, starting from top-left, going clockwise
    return [
        new THREE.Vector3(x, y + h, 0),     // top-left
        new THREE.Vector3(x + w, y + h, 0),  // top-right
        new THREE.Vector3(x + w, y, 0),      // bottom-right
        new THREE.Vector3(x, y, 0)           // bottom-left
    ];
}



function bboxToBox(bbox: number[]): THREE.Box3 {
    const [x, y, w, h] = bbox;
    
    // Create a new Box3 instance
    const box = new THREE.Box3();
    
    // Set the minimum point (lower-left corner in 2D)
    // For 2D to 3D conversion, we'll set z to 0
    box.min.set(x, y, 0);
    
    // Set the maximum point (upper-right corner in 2D)
    // Width and height are added to x and y respectively
    box.max.set(x + w, y + h, 0);
    
    return box;
}



export async function init(db:L33tMemDB) {
    PLCH = db.plcs['HUNDEGGER']  
    await PLCH.addBoundVariables([
        ".c1_Ftc1_Fila1_Presenza",
        ".TP_BLINK_1SEC_GLV",
        "RULLI_CENTRO_TAGLI.RCT_Semaforo.analog1"
    ])
}


// Export the run function that will be called by the task manager
// Your task logic here
export async function run(db:L33tMemDB) {
    let cam = db.agents["STREAM1"]
    if (!cam) return "No camera"



    cam.lastMessage?.detections?.forEach(d => {
        var box = bboxToVectors(d.bbox)
        var shape = pointsToVectors(cam.shapes["out"])
        var inside = new L33tGeometryUtil().polygonToPolygon(box,shape)

        console.log("inside ", inside)
    })


    PLCH = db.plcs['HUNDEGGER']

    //eturn `PEZZI no det`
    return `PEZZI ${cam.lastMessage ? cam.lastMessage.detections?.length : "no det"}`
}
