import { EventEmitter } from 'events';

export class L33tMCPHost extends EventEmitter {
    private protocol: ModelContextProtocol;
    private modelContext: Record<string, any> = {};
    private isRunning: boolean = false;

    constructor(options?: Partial<ProtocolOptions>) {
        super();
        
        const defaultOptions: ProtocolOptions = {
            name: 'L33t MCP Host',
            version: '1.0.0',
            supportedVersions: ['1.0'],
            ...options
        };

        this.protocol = new ModelContextProtocol(defaultOptions);
        this.setupEventHandlers();
    }

    private setupEventHandlers(): void {
        // Connection events
        this.protocol.on('connected', () => {
            this.isRunning = true;
            this.emit('connected');
            console.log('MCP Host connected');
        });

        this.protocol.on('disconnected', () => {
            this.isRunning = false;
            this.emit('disconnected');
            console.log('MCP Host disconnected');
        });

        // Model context updates
        this.protocol.on('contextUpdate', (update: ContextUpdate) => {
            this.modelContext = { ...this.modelContext, ...update };
            this.emit('contextUpdate', this.modelContext);
            console.log('Model context updated:', update);
        });

        // Error handling
        this.protocol.on('error', (error: Error) => {
            this.emit('error', error);
            console.error('MCP Host error:', error);
        });
    }

    /**
     * Starts the MCP protocol
     */
    public async start(): Promise<void> {
        if (this.isRunning) {
            console.warn('MCP Host is already running');
            return;
        }

        try {
            await this.protocol.connect();
            console.log('MCP Host started successfully');
        } catch (error) {
            console.error('Failed to start MCP Host:', error);
            throw error;
        }
    }

    /**
     * Stops the MCP protocol
     */
    public async stop(): Promise<void> {
        if (!this.isRunning) {
            console.warn('MCP Host is not running');
            return;
        }

        try {
            await this.protocol.disconnect();
            console.log('MCP Host stopped successfully');
        } catch (error) {
            console.error('Failed to stop MCP Host:', error);
            throw error;
        }
    }

    /**
     * Updates the model context
     */
    public async updateModelContext(context: Record<string, any>): Promise<void> {
        if (!this.isRunning) {
            throw new Error('MCP Host is not running');
        }

        try {
            await this.protocol.updateContext(context);
            this.modelContext = { ...this.modelContext, ...context };
            this.emit('contextUpdate', this.modelContext);
        } catch (error) {
            console.error('Failed to update model context:', error);
            throw error;
        }
    }

    /**
     * Gets the current model context
     */
    public getModelContext(): Record<string, any> {
        return this.modelContext;
    }

    /**
     * Registers a new capability
     */
    public registerCapability(name: string, handler: (params: any) => Promise<any>): void {
        try {
            this.protocol.registerCapability(name, handler);
            console.log(`Capability "${name}" registered successfully`);
        } catch (error) {
            console.error(`Failed to register capability "${name}":`, error);
            throw error;
        }
    }

    /**
     * Unregisters a capability
     */
    public unregisterCapability(name: string): void {
        try {
            this.protocol.unregisterCapability(name);
            console.log(`Capability "${name}" unregistered successfully`);
        } catch (error) {
            console.error(`Failed to unregister capability "${name}":`, error);
            throw error;
        }
    }

    /**
     * Checks if the host is running
     */
    public isHostRunning(): boolean {
        return this.isRunning;
    }
}
