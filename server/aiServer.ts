import express from 'express';
import { <PERSON><PERSON>ctionHand<PERSON> } from './AIActionHandler';

// Load environment variables
// config();

const app = express();
app.use(express.json());

// Initialize AI Action Handler
const aiHandler = new AIActionHandler(
    process.env.OPENAI_API_KEY!, 
    {
        // MCP specific options
    }
);

// Register some example capabilities
aiHandler.registerCapability('moveRobot', async (params) => {
    console.log('Moving robot:', params);
    return { success: true };
});

aiHandler.registerCapability('toggleLight', async (params) => {
    console.log('Toggling light:', params);
    return { success: true };
});

// Start the MCP connection
aiHandler.start().catch(console.error);

// API endpoints
app.post('/command', async (req, res) => {
    try {
        const { text } = req.body;
        await aiHandler.processCommand(text);
        res.json({ success: true });
    } catch (error) {
        console.error('Error processing command:', error);
        res.status(500).json({ 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
        });
    }
});

// Graceful shutdown
process.on('SIGTERM', async () => {
    await aiHandler.stop();
    process.exit(0);
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`AI Action Server listening on port ${PORT}`);
});