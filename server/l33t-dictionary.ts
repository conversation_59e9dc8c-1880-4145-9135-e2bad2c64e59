import { AdsState, AdsSymbolContainer } from "ads-client";

export class L33tThing {

}

export class L33tThingTimed extends L33tThing implements L33tTimed {
    timedLast: number = 0;
    timedPrevious: number = 0;
    timedTTL: number = 0;
}

export interface L33tTimed {
    timedLast: number,
    timedPrevious: number,
    timedTTL: number 
}

export interface L33tTimedInfo {
    timestamp: string
}

export class L33tTask implements L33tTimed {
    timedLast: number = 0;
    timedPrevious: number = 0;
    timedTTL = 500
    public _nodeinterval: NodeJS.Timeout | null = null;
    public path: string | null = null;
    public source: string| undefined;
    public scheduledInterval = 10000;
    lastResult?: string;
    isStarted?: boolean = false;
    fileContent: string = "";
    error: string | null = null;
    autoStart: boolean = false;

    toJSON() {
        return {
            timedTTL :this.timedTTL,
            timedLast: this.timedLast,
            timedPrevious : this.timedPrevious,
            path: this.path,
            scheduledInterval: this.scheduledInterval,
            fileContent:this.fileContent,
            isStarted: this.isStarted,
            lastResult:this.lastResult,
            error: this.error
        }
    }
}

export class L33tConfig {
    constructor(
        public zoom?: number,
        public rotation_angle?: number,
        public crop_x?: number,
        public crop_y?: number,
        public crop_width?: number,
        public crop_height?: number
    ) {}
  }
  
  export class L33tMessage {
    constructor(
        public iteration?: number,
        public image?: string,
        public time: number = 0,
        public memory_usage?: number,
        public detections?: Array<{
            class: string;
            confidence: number;
            bbox: number[];
        }>
    ) {
      this.time = time
    }
  }
  
  // Type definitions - StreamCanvas.tsx
  export type Point = { x: number; y: number };
  export type ShapeType = 'polygon' | 'line' | 'point';
  export interface DrawingCanvasProps {
    backgroundImage: string;
    initialShapes?: Record<string,Shape>;
    onSave: (shapes: Record<string,Shape>) => void;  // Add this line
  }
  
  export interface Shape {
    id: string;
    type: ShapeType;
    points: Point[];
    isClosed?: boolean;
  }
  
  export class L33tAgent implements L33tTimed {
    shapes: Shape[] = [];
    timedLast: number = 0;
    timedPrevious: number = 0;
    timedTTL: number = 0;
    
    constructor(
      public lastMessage?: L33tMessage,
      public config?: L33tConfig,
      shapes?: Shape[]
    ) {
      this.shapes = shapes || [];
    }
  
    toJSON() {
      return {
        lastMessage: this.lastMessage,
        config: this.config,
        shapes: this.shapes,
        timedLast: this.timedLast,
        timedPrevious: this.timedPrevious,
        timedTTL: this.timedTTL
      };
    }
  }

export type L33tMemDB = {
    agents: Record<string, L33tAgent> 
    tasks: { [id: string]: L33tTask }
    plcs: Record<string, L33tADSPLC> 
}


export interface L33tADSPLC extends L33tTimed {
    connected: boolean;
    description: string;
    ip: string;
    driver: string;
    timedLast: number;
    timedPrevious: number;
    timedTTL: number;
    symbols: AdsSymbolContainer | undefined;
    boundVariables: string[];
    values: { [key: string]: any };
    localAmsNetId: string;
    localAdsPort: number;
    targetAmsNetId: string;
    targetAdsPort: number;
    routerAddress: string;
    routerTcpPort: number;
    status?: AdsState;
    error: unknown;

    setHandler<T>(h: T): void;
    updateBoundVariables(varKeys: string[]): Promise<void>;
    addBoundVariables(varKeys: string[]): Promise<void>;

    writeValue(name:string, value:any):boolean

    toJSON(): {
        description: string;
        ip: string;
        driver: string;
        timedLast: number;
        timedPrevious: number;
        timedTTL: number;
        symbols: AdsSymbolContainer | undefined;
        boundVariables: string[];
        values: { [key: string]: any };
        connected: boolean;
        status?: AdsState;
        error: unknown;
    };
}



export class L33tADSPLCImpl implements L33tADSPLC {
    connected:boolean = false
    description: string = ""
    ip: string = ""
    driver: string =  ""
    timedLast: number = 0;
    timedPrevious: number = 0;
    timedTTL: number = 0;
    symbols: AdsSymbolContainer | undefined = undefined;
    boundVariables: string[] = []
    values: { [key: string]: any } = {};
    localAmsNetId!: string;
    localAdsPort!: number;
    targetAmsNetId!: string;
    targetAdsPort!: number;
    routerAddress!: string;
    routerTcpPort!: number;
    _handler:any = null;
    
    
    status?: AdsState;
  error: unknown;

    setHandler<AdsSockeantHandler>(h:AdsSockeantHandler) {
        this._handler = h
    }
    
    async updateBoundVariables(varKeys: string[]) {
       await this._handler?.updateBoundVariables(varKeys)
    }

    async addBoundVariables(varKeys: string[]) {
        
        var bound = async () => {
            await this._handler!.addBoundVariables(varKeys)
        }
    
        
        if (!this.connected)
            this._handler?.client.addListener("connect", bound)
        else
            bound()


        // if (!this._handler)
        //     console.error("Add bound var to a not properly initialize plc")
        // await this._handler?.addBoundVariables(varKeys)
     }

     writeValue(name: string, value: any) {
       this._handler.writeValue(name, value)
      return true  
     }

    toJSON() {
        return {
            description: this.description,
            ip: this.ip,
            driver: this.driver,
            timedLast: this.timedLast,
            timedPrevious: this.timedPrevious,
            timedTTL: this.timedTTL,
            symbols: this.symbols, // TODO implements fetching symbols via command.. don't serialize 
            boundVariables: this.boundVariables,
            values: this.values,
            connected: this.connected,
            status: this.status,
            error: this.error
        };
    }
}