# L33t Server

## Configuration

Each project requires a `l33t.json` configuration 

## Usage

### Development Mode

```bash
yarn dev --config projects/gali
```

### Production Mode (docker)

```bash
  docker-compose up --build
```


- `/projects` - Contains individual project configurations
  - `l33t.json` - Project-specific configuration file
  - Task files (`.mts`) - Contains task implementation code

## Requirements

- Node.js
- Yarn package manager
- Appropriate network access for PLC communications
- Valid project configuration files


## Publish on NPM

- change version in package.json file.
- npm publish --access restricted

you must to be logged as Pingendo in npm

## to install it
- once logged as Pingendo
- yarn add @pingendo/l33t-server