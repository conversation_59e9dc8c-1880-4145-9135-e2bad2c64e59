import { DefaultEventsMap, Server, Socket } from "socket.io";
import { L33tAgent, L33tMemDB } from "~/l33t-lib";
import { MemoryDB } from "./memdb";

export class AgentsManager {
 
  constructor(agentid: string, socket: Socket) {
    setInterval(() => {
      socket.emit("l33t_agent_ping", agentid)
    }, 5000);
  }
  
  pingAllClients() {

    //Come recupero l'ID e il socket da una funzione ESTERNA al pingAllClients??

    

  }
 
  registerHandler(io: Server, socket: Socket, memoryDB: MemoryDB) {
    

      socket.on("l33t_agent_pong", async id => {
        let agent = memoryDB.agents[id];
        memoryDB.agents[id].timedPrevious = agent.timedLast
        memoryDB.agents[id].timedLast = Date.now()
      })

      socket.on('l33t_agent_update', async ( id, data)=> {

        io.emit('l33t_agent_update', id,data)
        console.log("l33t_agent_update", id )
        memoryDB.agents[id] = data
      })

      io.on("l33t_agent_configupdate", async (id, data) => {
        socket.emit("l33t_agent_configupdate")
        io.emit("l33t_agent_update", id, data)
        console.log("Updated config")
      })
    
     /*  socket.on('l33t_read_symbols', async (data: { plcId: string }) => {
        try {
          // memoryDB.plcs
          // const symbols = await this.getSymbols();
          // socket.emit('l33t_read_symbols_response', { 
          //   plcId: data.plcId,
          //   symbols 
          // });
        } catch (error) {
          console.error('Error reading symbols:', error);
          socket.emit('l33t_read_symbols_response', { 
            plcId: data.plcId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }); */
    
    
    

  }

 
 initAll = async (
    memoryDB: L33tMemDB, 
    io: Server
  )  => {

  }

}