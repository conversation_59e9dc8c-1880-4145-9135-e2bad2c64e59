/**
 * streamRenderer.ts
 * Un plugin per il parsing in streaming di code fences Markdown e rendering colorato in console.
 */
import process from 'process';
import readline from 'readline';
import chalk from 'chalk';
import boxen from 'boxen';

// Tipi di fence supportati
type FenceType = 'html' | 'json' | 'action' | null;

/**
 * Rendering di blocco HTML in un box colorato
 */
function renderHTML(html: string) {
  const boxed = boxen(html.trim(), {
    padding: 1,
    margin: 1,
    borderColor: 'cyan',
    borderStyle: 'round',
  });
  console.log(chalk.white(boxed));
}

/**
 * Rendering di JSON formattato con indentazione e colori
 */
function renderJSON(jsonStr: string) {
  try {
    const obj = JSON.parse(jsonStr);
    const pretty = JSON.stringify(obj, null, 2);
    console.log(chalk.yellowBright(pretty));
  } catch (err) {
    console.error(chalk.red('JSON non valido:'), err.message);
  }
}

/**
 * Rendering di action colorate
 */
function renderAction(actionText: string) {
  console.log(chalk.greenBright(`⚡ Action: ${actionText.trim()}`));
}

/**
 * Funzione principale per il parsing dello stdin
 */
function run() {
  let fenceType: FenceType = null;
  let buffer = '';

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: false,
  });

  rl.on('line', (line: string) => {
    const startMatch = line.match(/^```(\w+)/);

    if (startMatch) {
      const lang = startMatch[1];
      if (lang === 'html' || lang === 'json' || lang === 'action') {
        fenceType = lang;
        buffer = '';
        return;
      }
    }

    // Fine del fence
    if (line.trim() === '```' && fenceType) {
      switch (fenceType) {
        case 'html':
          renderHTML(buffer);
          break;
        case 'json':
          renderJSON(buffer);
          break;
        case 'action':
          renderAction(buffer);
          break;
      }
      fenceType = null;
      buffer = '';
      return;
    }

    // Dentro un fence
    if (fenceType) {
      buffer += line + '\n';
    } else {
      // Output normale per linee non fence
      console.log(line);
    }
  });

  rl.on('close', () => {
    // Se termina l'input mentre siamo ancora in un fence aperto
    if (fenceType && buffer) {
      switch (fenceType) {
        case 'html':
          renderHTML(buffer);
          break;
        case 'json':
          renderJSON(buffer);
          break;
        case 'action':
          renderAction(buffer);
          break;
      }
    }
  });
}

// Se eseguito direttamente, avvia il parser
if (require.main === module) {
  run();
}
