import { Server as SocketServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import express from 'express';
import { BasePlugin } from '../types/Plugin';
import { L33tPrompt } from '../L33tPrompt';
import { ModelContextProtocolClient } from '../services/ChatGPTService';
import gradient from 'gradient-string';
import { join, dirname } from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import clipboardy from 'clipboardy';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface ChatMessage {
    role: 'user' | 'assistant' | 'mcp-result';
    content: string;
    timestamp: Date;
    mcpResults?: any[];
}

export default class ChatGPTPlugin extends BasePlugin {
    private mcpClient: ModelContextProtocolClient;
    private conversationHistory: ChatMessage[] = [];
    private readonly MAX_HISTORY = 10;

    constructor() {
        super('Jarvis', '1.0.0');
        const configPath = join( 'config.json');
        const configUrl = pathToFileURL(configPath).href;

        console.log(configUrl)
        this.mcpClient = new ModelContextProtocolClient(configUrl);
    }

    async init(app: express.Application, server: HttpServer, io: SocketServer): Promise<void> {
        // await this.mcpClient.initialize();
        console.log('ChatGPT plugin initialized');

        this.addMenuItem({
            name: 'Hei Jarvis',
            value: 'chatgpt',
            handler: async () => this.showChatInterface()
        });
    }

    private async showChatInterface() {
        const renderHTML = (html: string) => {
            console.log('\n[HTML Snippet Rendered]');
            console.log(html.trim().substring(0, 100));
            console.log('[End HTML Snippet]\n');
        };
        const renderJSON = (jsonText: string) => {
            try {
                const obj = JSON.parse(jsonText);
                const singleLine = JSON.stringify(obj);
                
                // Check if it's an array of actions or a single action object
                if (Array.isArray(obj)) {
                    const hasActions = obj.some(item => item.action);
                    if (hasActions) {
                        
                        let ml = obj.map(o => o.action + JSON.stringify(o)).join('\n - ');
                        console.log(`\n${gradient.pastel.multiline('⚡ [ACTIONS] ')}\n - ${ml}\n`);

                    } else {
                        console.log(`\n[JSON ARRAY] ${singleLine}\n`);
                    }
                } else if (obj.action) {
                    console.log(`\n${gradient.pastel.multiline('⚡ [ACTION] ')}${singleLine}\n`);
                } else {
                    console.log(`\n[JSON] ${singleLine}\n`);
                }
            } catch {
                console.log(`\n[Invalid JSON] ${jsonText}\n`);
            }
        };
        const isJsonString = (str: string) => {
            try { JSON.parse(str); return true; } catch { return false; }
        };

        console.log(gradient.pastel.multiline('\nHey Jarvis 🤖\n'));
        while (true) {

            // if (this.conversationHistory.length > 0) {
            //     console.log(gradient.rainbow('\nRecent conversation:\n'));
            //     this.conversationHistory.slice(-3).forEach(msg => {
            //         const prefix = msg.role === 'user' ? '👤 You: ' : '🤖 Jarvis: ';
            //         const htmlMatch = msg.content.match(/```html\s*([\s\S]*?)```/);
            //         const jsonMatch = msg.content.match(/```json\s*([\s\S]*?)```/);
            //         if (htmlMatch) {
            //             console.log(prefix);
            //             renderHTML(htmlMatch[1]);
            //         } else if (jsonMatch) {
            //             console.log(prefix);
            //             renderJSON(jsonMatch[1]);
            //         } else if (isJsonString(msg.content.trim())) {
            //             console.log(prefix);
            //             renderJSON(msg.content);
            //         } else if (msg.content.trim().startsWith('<')) {
            //             console.log(prefix);
            //             renderHTML(msg.content);
            //         } else {
            //             console.log(`${prefix}${msg.content}\n`);
            //         }
            //     });
            //     console.log('------------------------\n');
            // }

            const question = await L33tPrompt.input('');
            if (question.trim() === '') {
                console.log(gradient.pastel.multiline('\nType "quit" to exit the chat\n'));
                continue;
            }
            if (question.toLowerCase() === 'quit') {
                console.log(gradient.pastel.multiline('\nGoodbye! 👋\n'));
                break;
            }

            try {
                this.addToHistory('user', question);
                console.log('\nThinking...\n');

                let buffer = '';
                let fullResponse = '';

                process.stdout.write(gradient.rainbow('🤖 '));
                const { answer, mcpResults } = await this.mcpClient.askAndExecute(question, (token: string) => {
                    buffer += token;
                    fullResponse += token;
                    process.stdout.write(token);
                });

                // Add the assistant's response to history
                this.addToHistory('assistant', fullResponse);

                // If there are MCP results, add them to history as a separate entry
                if (mcpResults && mcpResults.length > 0) {
                    this.addToHistory('mcp-result', JSON.stringify(mcpResults, null, 2), mcpResults);
                }   

                // Salvataggio della risposta nella history solo dopo aver completato tutto
                this.addToHistory('assistant', fullResponse);

            } catch (error) {
                console.error('\nError:', error);
                await L33tPrompt.confirm('Press Enter to continue...');
            }
        }
    }

    private addToHistory(role: 'user' | 'assistant' | 'mcp-result', content: string, mcpResults?: any[]) {
        this.conversationHistory.push({ 
            role, 
            content, 
            timestamp: new Date(),
            mcpResults 
        });
        
        if (this.conversationHistory.length > this.MAX_HISTORY) {
            this.conversationHistory = this.conversationHistory.slice(-this.MAX_HISTORY);
        }
    }
}
