{"name": "@pingendo/l33t-server", "version": "1.0.7", "type": "module", "private": true, "scripts": {"dev": "npx tsx ./index.ts ", "start": "cross-env NODE_ENV=production npx tsx ./index.ts", "build": "tsc", "test": "jest", "lint": "eslint --ext .ts ."}, "dependencies": {"@modelcontextprotocol/sdk": "^1.10.1", "@remix-run/express": "^2.9.2", "@remix-run/node": "^2.9.2", "@types/ads-client": "^1.14.6", "ads-client": "^2.0.1", "boxen": "^8.0.1", "clipboardy": "^4.0.0", "dotenv": "^16.5.0", "express": "^4.18.2", "gradient-string": "^3.0.0", "inquirer": "^12.5.2", "morgan": "^1.10.0", "openai": "^4.95.1", "ping": "^0.4.4", "socket.io": "^4.8.1"}, "devDependencies": {"@types/express": "^4.17.20", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.9", "@types/node": "^22.14.1", "@types/ping": "^0.4.4", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "cross-env": "^7.0.3", "eslint": "^8.38.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "tsx": "^4.19.1", "typescript": "^5.7.2"}, "engines": {"node": ">=22.0.0"}}