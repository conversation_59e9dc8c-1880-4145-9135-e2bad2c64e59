nome	luogo / posizione	tipo	ip	marca / modello	persone coinvolte	cosa fa / cosa serve	healtcheck	Server MCP di riferimento	
Capannone 4	Capannone 4	luogo	non ha un ip e' un luogo			Ospita  il robot e il carroponte del GBS			
Capannone 3	Capannone 3	luogo	non ha un ip e' un luogo						
Capannone 2	Capannone 2	luogo	non ha un ip e' un luogo						
Capannone 1	Capannone 1	luogo	non ha un ip e' un luogo						
Piazzale	Piazzale	luogo	non ha un ip e' un luogo						
Cancello	Cancello	luogo	non ha un ip e' un luogo			"serve per leggere le targhe delle auto e camion che entrano in galimberti.
sappiamo targa e timestamp"			
Ufifici						contiene sala server			
Hundegger	Capannone 3 sud	Centro di lavoro							
plc zona 1-2		PLC	**************	Beckhoff twincat3	giulio, thomas, tiesse, alan	scarico per hundegger fatto da noi			
plc zona 3		PLC	**************	Beckhoff twincat3	giulio, thomas, ties<PERSON>, alan	scarico dopo vidali			
Valmec Tetti			**************		bovo				
Valmec Verniciatura					bovo				
Carroponte	Capannone 4	PLC		Beckhoff twincat2	giulio, marco gerosa,alan	<PERSON>S			
Traversa carroponte	Capannone 4			Beckhoff twincat2	giulio, marco gerosa,alan	GBS			
PC robot	Capannone 4		**************		giulio, marco gerosa,alan	GBS			
Robot kuka	Capannone 4				giulio, marco gerosa,alan	GBS			
QNAP Backup	sala server		*************:8080	QNAP			pingo il suo ip, provo se accedo via SMB alla shared folder /sistema 		
QNAP GALI TS-431	sala server		**************	QNAP TS-431			pingo il suo ip, provo se accedo via SMB alla shared folder /sistema 		
NVR unifi	sala server		*************				pingo *******		
Dreamachine	sala server		************						
router telecom			*************						
starlink						backup connessione internet			
allarme									
centrale Konnex									
Aspirazione		PLC							
Thor	sala server	pc desktop	**************		giulio, alan	container docker, essiccatoio, server multiutenza	pingo il suo ip	server mcp ping    	
Thor2	sala server	pc desktop			giulio, alan	odoo	pingo il suo ip		
Muletto 1									
Muletto 2									
Odoo					marco	"serve per leggere ordini di vendita, ordini di acquisto, picking, produzioni, 
info sul magazzino prodotti giacenze e anagrafiche clienti e fornitori"			
Essicatoio									
Shelly Essetre	Quadro Essetre	energy monitor	************	Shelly Pro 3EM	alan, gabriele	monitora i consumi			
Shelly Hundegger	Quadro Essetre	energy monitor	************	Shelly Pro 3EM	alan, gabriele	monitora i consumi			
PLC Compattabile			*************						
Antincendio	sala server		*************0	Inim	alan, paganini, sandro	gestisce impianto antincendio			
Vecchio ESX server							< 		"per ripartire..
ssh -oHostKeyAlgorithms=+ssh-rsa -oPubkeyAcceptedKeyTypes=+ssh-rsa -oMACs=+hmac-sha1,hmac-sha1-96 root@************


vim-cmd vmsvc/power.on 6

vim-cmd vmsvc/power.on 7

aspetta che salga la virtual machine del domain controller qlc minuto



poi 

vim-cmd vmsvc/power.on 10


ci mette un botto"
Gateway ikon luci e riscaldamento	Capannone 4 nord	gateway	**************	Ikon	alan, sandro	controllo delle luci e del riscaldamento			