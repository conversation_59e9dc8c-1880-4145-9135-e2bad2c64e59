import { Server, Socket } from 'socket.io';
import { ModelContextProtocolClient } from './ChatGPTService';

export class SocketIOService {
  private chatHistory: { role: string; content: string; mcpResults?: any[] }[] = [];
  private totalConnections = 0;
  private peakConnections = 0;

  constructor(
    private io: Server,
    private chatGPTService: ModelContextProtocolClient
  ) {}

  public initialize(): void {
    this.setupSocketHandlers();
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      this.handleNewConnection(socket);
      this.setupChatHandlers(socket);
      this.setupDisconnectHandler(socket);
    });
  }

  private handleNewConnection(socket: Socket): void {
    this.totalConnections++;
    this.peakConnections = Math.max(this.peakConnections, this.io.sockets.sockets.size);
    
    console.log('Chat client connected:', socket.id);
    socket.emit('welcome', 'Welcome to the L33T Server!');
  }

  private setupChatHandlers(socket: Socket): void {
    // Handle chat messages
    socket.on('chat_message', async (message: string) => {
      try {
        console.log('Received chat message:', message);

        // Add user message to history
        this.chatHistory.push({
          role: 'user',
          content: message
        });

        // Stream the AI response
        let responseText = '';
        const { answer, mcpResults } = await this.chatGPTService.askAndExecute(
          message,
          (token: string) => {
            responseText += token;
            socket.emit('chat_response_stream', { token });
          },
          this.chatHistory
        );

        // Emit complete response
        socket.emit('chat_response_complete', {
          response: responseText,
          mcpResults
        });

        // Add AI response to history
        this.chatHistory.push({
          role: 'assistant',
          content: responseText,
          mcpResults
        });

      } catch (error) {
        console.error('Error processing chat message:', error);
        socket.emit('chat_error', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Handle history request
    socket.on('get_chat_history', () => {
      console.log('Sending chat history to client:', socket.id);
      socket.emit('chat_history', this.chatHistory);
    });

    // Handle history clear
    socket.on('clear_chat_history', () => {
      console.log('Clearing chat history for client:', socket.id);
      this.chatHistory = [];
      socket.emit('chat_history_cleared');
      this.io.emit('chat_history', this.chatHistory); // Broadcast to all clients
    });
  }

  private setupDisconnectHandler(socket: Socket): void {
    socket.on('disconnect', () => {
      console.log('Chat client disconnected:', socket.id);
    });

    socket.on('error', (error: Error) => {
      console.error('Socket error:', error);
    });
  }

  // Public methods for external use
  public broadcastSystemMessage(message: string): void {
    this.io.emit('system_message', {
      timestamp: new Date(),
      content: message
    });
  }

  public getConnectedClientsCount(): number {
    return this.io.sockets.sockets.size;
  }

  public getTotalConnections(): number {
    return this.totalConnections;
  }

  public getPeakConnections(): number {
    return this.peakConnections;
  }
}
