import { readFile } from 'fs/promises';
import OpenAI from 'openai';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { config } from 'dotenv';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { z } from 'zod';

/**
 * Configurazione per ciascun server MCP
 */
interface MCPServerConfig {
  name: string;            // Nome logico del server
  resourceUri: string;     // URI della risorsa per ottenere il contesto
  url: string;            // URL del server MCP
}

/**
 * Struttura del file di configurazione JSON
 */
interface Config {
  servers: MCPServerConfig[];
  openaiApiKey: string;
  model: string;
}

const CommandSchema = z.object({
  server: z.string(),
  action: z.string(),
  arguments: z.record(z.unknown()), // Changed from 'args' to 'arguments'
});
type Command = z.infer<typeof CommandSchema>;

/**
 * Client MCP + ChatGPT: legge contesti da più server MCP e invia domande a ChatGPT
 */
export class ModelContextProtocolClient {
  private config!: Config;
  private openai: OpenAI;
  private clients: Map<string, Client> = new Map();

  constructor(private configPath: string = '../server/config.json') {  // default path
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY not found in environment variables');
    }
    this.openai = new OpenAI({ apiKey });

    // If configPath is a URL, convert it to a filesystem path
    if (configPath.startsWith('file://')) {
      this.configPath = fileURLToPath(configPath);
    }

    if (!this.clients.size) {
      this.loadConfig();
    }
  }

  private async initializeClient(srv: MCPServerConfig): Promise<Client> {
    const client = new Client({ 
      name: `L33t Context Client - ${srv.name}`, 
      version: '1.0.0' 
    });
    
    const transport = new StreamableHTTPClientTransport(
      new URL(srv.url)
    );

    await client.connect(transport);
    return client;
  }

  /**
   * Carica e valida la configurazione dal file JSON
   */
  private async loadConfig(): Promise<void> {
    try {
      const raw = await readFile(this.configPath, 'utf8');
      this.config = JSON.parse(raw) as Config;
      
      // TODO
      this.openai = new OpenAI({ apiKey: this.config.openaiApiKey });

      // Initialize clients for each server
      for (const srv of this.config.servers) {
        const client = await this.initializeClient(srv);
        this.clients.set(srv.name, client);
      }
    } catch (error) {
      console.error(`Failed to load config from ${this.configPath}:`, error);
      throw error;
    }
  }

  /**
   * Interroga ciascun server MCP per ottenere e concatenare i contesti
   */
  private async buildContext(): Promise<string> {
    const parts: string[] = [];
    for (const srv of this.config.servers) {
      try {
        const client = this.clients.get(srv.name);
        if (!client) {
          throw new Error(`Client not initialized for server ${srv.name}`);
        }

        // Get available resources (unwrap the .resources array)
        const resPayload = await client.listResources();
        const resources = Array.isArray(resPayload)
          ? resPayload
          : (resPayload as { resources?: any[] }).resources ?? [];
          

        // Read the specific resource
        const resource = await client.readResource({ uri: srv.resourceUri });
        const text = resource.contents.map(c => c.text).join('\n');

        
        parts.push(`## Context from ${srv.name}\n${text}`);

        // Call any available tools (unwrap the .tools array)
        const toolsPayload = await client.listTools();
        const tools = Array.isArray(toolsPayload)
          ? toolsPayload
          : (toolsPayload as { tools?: any[] }).tools ?? [];
        for (const tool of z.array(z.any()).parse(tools)) {
          console.log(`Available tool in ${srv.name}:`, tool);
        }
        parts.push(`## Tools from ${srv.name}\n${JSON.stringify(tools, null, 2)}`);
      } catch (err) {
        console.warn(`Failed to fetch context from ${srv.name}:`, err);
      }
    }
    return parts.join('\n\n');
  }

  /**
   * Estrae e esegue i comandi MCP da una stringa di risposta ChatGPT
   */
  private async executeMcpCommands(rawResponse: string): Promise<any[]> {
    const jsonMatch = rawResponse.match(/(\[\s*\{[\s\S]*\}\s*\])/);
    if (!jsonMatch) {
      return [];
    }

    let commands: Command[];
    try {
      const parsed = JSON.parse(jsonMatch[1]);
      commands = z.array(CommandSchema).parse(parsed);
    } catch (err) {
      console.error('Errore nel parsing/validazione dei comandi MCP:', err);
      return [];
    }

    const results: any[] = [];
    for (const cmd of commands) {
      const client = this.clients.get(cmd.server);
      if (!client) {
        throw new Error(`Client MCP non inizializzato per server "${cmd.server}"`);
      }

      try {
        const res = await client.callTool({
          name: cmd.action,
          arguments: cmd.arguments
        });

        results.push({ 
          command: cmd, 
          result: res.content 
        });
      } catch (toolErr) {
        console.error('Errore eseguendo tool ' + JSON.stringify(cmd) + ':', toolErr);
        results.push({ 
          command: cmd, 
          error: toolErr 
        });
      }
    }

    return results;
  }

  /**
   * Versione streaming di ask che emette la risposta carattere per carattere
   * @param question La domanda dell'utente
   * @param onToken Callback chiamata per ogni token della risposta
   */
  public async ask(
    question: string,
    onToken: (token: string) => void,
    history?: { role: string; content: string; mcpResults?: any[] }[]
  ): Promise<string> {
    if (!this.clients.size) {
      await this.loadConfig();
    }
    
    let contextText = await this.buildContext();
    
    // Add conversation history with MCP results to context
    if (history?.length) {
      contextText += '\n\n## Conversation History\n';
      history.forEach(msg => {
        contextText += `\n${msg.role.toUpperCase()}: ${msg.content}`;
        if (msg.mcpResults) {
          contextText += '\nMCP Results:\n' + JSON.stringify(msg.mcpResults, null, 2);
        }
      });
    }

    const systemContent = `
      Ti chiami Jarvis, lavori per la Galimberti srl di Lomagna provincia di Lecco. 
      Sei un orchestratore MCP che conosce già la lista dei server disponibili e i loro schemi di operazione.
      
      Per ogni domanda utente, 
      anteponi una risposta in italiano per l'utente,
      e poi devi inserire se richiesto un array JSON di comandi, ognuno con questa forma:

      [
        {
          "server": "ping",                           // deve corrispondere alla colonna server MCP di riferimento nei devices
          "action": "",                           // deve essere uno dei tool disponibili per quel server 
          "arguments": {                          // se non servono argomenti, lascia un oggetto vuoto
            // ad esempio: "host": "***********"
          }
        },
        …
      ]

      Se l'array non contien **non** devi inserire nulla.
      **Non** aggiungere mai testo libero o commenti: solo JSON valido, pronto per essere passato al ciclo di esecuzione.
      Puoi usare emoticons e termini informali.
      Puoi creare html.
    `.trim();

    let devicesContext = '';
    try {
      const devicesPath = join('context', 'devices.csv');
      const devicesContent = await readFile(devicesPath, 'utf8');
      devicesContext = `\n\n## Devices Information\nEcco l'elenco dei dispositivi e la loro configurazione:\n\n${devicesContent}`;
    } catch (err) {
      console.warn('Failed to read devices.csv:', err);
    }

    const messages = [
      { 
        role: 'system' as const, 
        content: `
          ${systemContent}  
          Ecco il contesto dai server MCP \n
          ${contextText}
          \n
          e l'elenco dei dispositivi:\n${devicesContext}` 
      },
      { role: 'user' as const, content: question }
    ];

    let fullResponse = '';


    const stream = await this.openai.chat.completions.create({
      model: this.config.model,
      messages,
      stream: true,
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        fullResponse += content;
        onToken(content);
      }
    }

    return fullResponse.trim();
  }

  /**
   * Versione estesa di ask che esegue anche i comandi MCP
   */
  public async askAndExecute(
    question: string,
    onToken: (token: string) => void,
    history?: { role: string; content: string; mcpResults?: any[] }[]
  ): Promise<{ answer: string; mcpResults: any[] }> {
    
    // Fase 1: Estrazione comandi
    const commandExtraction = await this.ask(question, onToken, history); // Pass the actual onToken callback
    const mcpResults = await this.executeMcpCommands(commandExtraction);

    // Se non ci sono risultati MCP, restituisci la risposta originale
    if (!mcpResults.length) {
      return { answer: commandExtraction.replace(/(\[\s*\{[\s\S]*\}\s*\])/, '').trim(), mcpResults: [] };
    }

    // Fase 2: Generazione risposta finale con i risultati
    const followUpMessages = [
      { 
        role: 'system' as const, 
        content: `
          Ti chiami Jarvis, lavori per la Galimberti srl di Lomagna provincia di Lecco.
          Rispondi in italiano in modo chiaro e colloquiale, spiegando i risultati delle operazioni eseguite.
          Puoi usare emoticons e termini informali.
        `.trim() 
      },
      { 
        role: 'user' as const, 
        content: `
          L'utente ha chiesto: "${question}"

          Ho eseguito questi comandi MCP e ottenuto i seguenti risultati:
          ${JSON.stringify(mcpResults, null, 2)}

          Per favore, analizza questi risultati e fornisci una risposta chiara e dettagliata in italiano.
        `.trim() 
      }
    ];

    let finalAnswer = '';
    const stream = await this.openai.chat.completions.create({
      model: this.config.model,
      messages: followUpMessages,
      stream: true,
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        finalAnswer += content;
        onToken(content);
      }
    }

    // Aggiorna la history con i risultati MCP
    if (history) {
      history.push({
        role: 'mcp-result',
        content: JSON.stringify(mcpResults, null, 2),
        mcpResults
      });
    }

    return { 
      answer: finalAnswer.trim(), 
      mcpResults 
    };
  }

  public async disconnect(): Promise<void> {
    for (const [name, client] of this.clients) {
      try {
        await client.close();
        console.log(`Disconnected client for server ${name}`);
      } catch (err) {
        console.warn(`Error disconnecting client for server ${name}:`, err);
      }
    }
    this.clients.clear();
  }
}
