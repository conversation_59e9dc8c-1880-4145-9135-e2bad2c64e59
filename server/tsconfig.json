{
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.mts",
    "**/.server/**/*.ts",
    "**/.server/**/*.tsx",
    "**/.client/**/*.ts",
    "**/.client/**/*.tsx",
    "tests/**/*"
, "app/model/l33t-memdb.ts", "projects/gali/index.mts"  ],
  "compilerOptions": {
    "declaration": true,
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": [],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "esnext",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["./*"],
      "l33t-lib": ["./l33t-lib"]
    },

    // Vite takes care of building everything, not tsc.
    "noEmit": true
  }
}
