import { McpServer, ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import { randomUUID } from 'node:crypto';
import { z } from 'zod';
import express from 'express';

interface PingConfig {
    numeric?: boolean;
    timeout?: number;
    deadline?: number;
    min_reply?: number;
    v6?: boolean;
    sourceAddr?: string;
    packetSize?: number;
    extra?: string[];
}

interface PingResponse {
    host: string;
    numeric_host?: string;
    alive: boolean;
    output: string;
    time: number;
    times?: number[];
    min?: string;
    max?: string;
    avg?: string;
    stddev?: string;
}

interface PingStatusHistory {
    host: string;
    status: 'up' | 'down';
    latency?: number;
    timestamp: string;
    details?: {
        output: string;
        min?: string;
        max?: string;
        avg?: string;
    };
    error?: string;
}

interface ToolParams {
    host: string;
    timeout?: number;
    packetSize?: number;
}

export class MCPServerPing {
    private server: McpServer;
    private transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};
    private pingHistory: Map<string, PingStatusHistory[]> = new Map();
    private readonly MAX_HISTORY_PER_HOST = 100;

    constructor() {
        this.server = new McpServer({
            name: 'L33t Ping Server',
            version: '1.0.0',
        });

        const pingResult: PingStatusHistory = {
            "host":"*******",
            status: 'up',
            latency: undefined ,
            timestamp: new Date().toISOString(),
           
        };

        // Store the result in history
        this.addToPingHistory(pingResult);

    }

    public setup(app: express.Application) {
        app.use(express.json());

        // Handle POST requests for client-to-server communication
        app.post('/mcp/ping', async (req, res) => {
            const sessionId = req.headers['mcp-session-id'] as string | undefined;
            let transport: StreamableHTTPServerTransport;

            if (sessionId && this.transports[sessionId]) {
                transport = this.transports[sessionId];
            } else if (!sessionId && isInitializeRequest(req.body)) {
                transport = new StreamableHTTPServerTransport({
                    sessionIdGenerator: () => randomUUID(),
                    onsessioninitialized: (sessionId) => {
                        this.transports[sessionId] = transport;
                    }
                });

                transport.onclose = () => {
                    if (transport.sessionId) {
                        delete this.transports[transport.sessionId];
                    }
                };

                await this.server.connect(transport);

            } else {
                res.status(400).json({
                    jsonrpc: '2.0',
                    error: {
                        code: -32000,
                        message: 'Bad Request: No valid session ID provided',
                    },
                    id: null,
                });
                return;
            }

            await transport.handleRequest(req, res, req.body);
        });

        // Handle GET and DELETE requests
        const handleSessionRequest = async (req: express.Request, res: express.Response) => {
            const sessionId = req.headers['mcp-session-id'] as string | undefined;
            if (!sessionId || !this.transports[sessionId]) {
                res.status(400).send('Invalid or missing session ID');
                return;
            }
            
            const transport = this.transports[sessionId];
            await transport.handleRequest(req, res);
        };

        app.get('/mcp/ping', handleSessionRequest);
        app.delete('/mcp/ping', handleSessionRequest);

        this.setupResources();
        this.setupTools();
    }


    private setupResources() {
        // 1) Template dinamico per singolo host
        this.server.resource(
          "ping-status-by-host",  
          new ResourceTemplate("ping-status://{host}", {
            list: async (_extra) => ({    // ListResourcesCallback
              resources: Array.from(this.pingHistory.keys()).map(host => ({
                uri:      `ping-status://${host}`,
                name:     host,
                mimeType: "application/json"
              }))
            })
          }),
          async (uri, variables, _extra) => {   // ReadResourceTemplateCallback
            // `variables.host` è inferito correttamente
            if (Array.isArray(variables.host)) {
              throw new Error('Multiple hosts not supported yet');
            }
            const history = this.pingHistory.get(variables.host) ?? [];
            return {
              contents: [{
                uri:  uri.href,
                type: "application/json",
                text: JSON.stringify(history, null, 2)
              }]
            };
          }
        );
      
        // 2) Risorsa statica “all” per tutta la cronologia
        this.server.resource(
          "ping-status-all",  
          "ping-status://all",
          async (uri, _extra) => {    // ReadResourceCallback
            const allHistory = Array.from(this.pingHistory.values()).flat();
            return {
              contents: [{
                uri:  uri.href,
                type: "application/json",
                text: JSON.stringify(allHistory, null, 2)
              }]
            };
          }
        );
      }
      
      
      
    // private setupResources() {
    //     // Set up the resource to read the history

    
    //     this.server.resource(
    //         "ping-status://all",
    //         new ResourceTemplate("ping-status://all", { list: undefined }),
    //         async (uri) => {
    //             return {
    //                 contents: [{
    //                     uri: uri.href,
    //                     type: "text",
    //                     text: "aaa",
    //                 }]
    //             };
    //         }
    //     );
    // }

    

    private setupTools() {
        const PingShape = {
            host: z.string().describe("L'indirizzo IP o hostname da pingare"),
        };

        this.server.tool(
            "ping",
            PingShape,
            async ({ host }: ToolParams) => {
                try {
                    const ping = (await import('ping')).default;
                    const config: PingConfig = {
                        timeout: 3,
                        packetSize: 56
                    };
                    if (Array.isArray(host))
                        throw new Error('Multiple hosts not supported yet');

                    const result = await ping.promise.probe(host, config);
                    const pingResult: PingStatusHistory = {
                        host,
                        status: result.alive ? 'up' : 'down',
                        // latency: result.time === "unknown" ? undefined : parseFloat(result.time),
                        timestamp: new Date().toISOString(),
                        details: {
                            output: result.output,
                            min: result.min === "unknown" ? undefined : result.min,
                            max: result.max === "unknown" ? undefined : result.max,
                            avg: result.avg === "unknown" ? undefined : result.avg
                        }
                    };

                    this.addToPingHistory(pingResult);

                    return {
                        content: [{
                            type: "text",
                            text: JSON.stringify(pingResult)
                        }]
                    };
                } catch (error) {
                    const errorResult: PingStatusHistory = {
                        host,
                        status: 'down',
                        timestamp: new Date().toISOString(),
                        error: error instanceof Error ? error.message : 'Unknown error'
                    };

                    this.addToPingHistory(errorResult);

                    return {
                        content: [{
                            type: "text",
                            text: JSON.stringify(errorResult)
                        }]
                    };
                }
            }
            
        );
    }

    private addToPingHistory(result: PingStatusHistory) {
        const history = this.pingHistory.get(result.host) || [];
        history.unshift(result);  // Add new result at the beginning
        // Keep only the last MAX_HISTORY_PER_HOST entries
        if (history.length > this.MAX_HISTORY_PER_HOST) {
            history.length = this.MAX_HISTORY_PER_HOST;
        }
        this.pingHistory.set(result.host, history);
    }

   
    public async stop(): Promise<void> {
        // Close all active transports
        await Promise.all(
            Object.values(this.transports).map(transport => transport.close())
        );
        this.transports = {};
        console.log('MCP Ping Server stopped');
    }
}

