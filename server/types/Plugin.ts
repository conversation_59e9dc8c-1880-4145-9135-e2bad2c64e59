import { Server as SocketServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import express from 'express';

export interface MenuItem {
    name: string;
    value: string;
    handler: () => Promise<void>;
}

export interface Plugin {
    name: string;
    version: string;
    init(app: express.Application, server: HttpServer, io: SocketServer): Promise<void>;
    getMenuItems(): MenuItem[];
}

export abstract class BasePlugin implements Plugin {
    private menuItems: MenuItem[] = [];

    constructor(
        public readonly name: string,
        public readonly version: string
    ) {}

    protected addMenuItem(item: MenuItem) {
        this.menuItems.push(item);
    }

    getMenuItems(): MenuItem[] {
        return this.menuItems;
    }

    abstract init(app: express.Application, server: HttpServer, io: SocketServer): Promise<void>;
}
