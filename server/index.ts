import 'dotenv/config';
import { L33tThing, L33tThingTimed } from "./l33t-dictionary";
import { createServer } from "http";
import express from "express";
import { Server } from "socket.io";
import { PluginManager } from "./PluginManager";
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import gradient from 'gradient-string';
import { L33tPrompt } from './L33tPrompt';
import { ModelContextProtocolClient } from "./services/ChatGPTService";
import { MCPServerPing } from './mcp/MCPPingServer';
import { SocketIOService } from './services/SocketIOService';

// ES Modules equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create Express app
const app = express();
const httpServer = createServer(app);

// Create Socket.IO server
const io = new Server(httpServer, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Initialize services
const chatGPTService = new ModelContextProtocolClient();
const socketIOService = new SocketIOService(io, chatGPTService);

// Initialize plugin manager
const pluginsDir = join(__dirname, 'plugins');
const pluginManager = new PluginManager(pluginsDir, app, httpServer, io);

// Server startup sequence
const startServer = async () => {
    console.log(gradient.pastel.multiline('\nInitializing L33T Server...\n'));
    
    try {
        // Initialize Socket.IO service
        socketIOService.initialize();
        
        // Load plugins
        await pluginManager.loadPlugins();
        const plugins = pluginManager.getLoadedPlugins();
        console.log(gradient.pastel.multiline(plugins.length + ' plugins started...\n'));

        // Start server
        const PORT = process.env.PORT || 1337;
        httpServer.listen(PORT, () => {
            console.log(gradient.pastel.multiline('\nStarted...\n'));
        });
    } catch (error) {
        console.log(`Failed to start server: ${error}`);
        process.exit(1);
    }
};






// Socket.IO connection handler is now in SocketIOService

// Graceful shutdown
const shutdown = async () => {
    console.log(gradient.rainbow('\nShutting down L33T Server...'));
    
    // Chiudi tutte le connessioni socket attive
    io.close();
    
    // Chiudi il server HTTP e aspetta che termini
    await new Promise<void>((resolve) => {
        httpServer.close(() => {
            console.log('Server shutdown complete');
            resolve();
        });
    });
    
    // Esci dal processo
    process.exit(0);
};

// Error handling
process.on("uncaughtException", (error) => {
    console.log(`Uncaught Exception: ${error.message}`);
    // shutdown();
});

process.on("unhandledRejection", (reason, promise) => {
    console.log(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    // shutdown();
});

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

async function showMainMenu() {
    while (true) {
        // Pulisci lo schermo
        // process.stdout.write('\x1Bc');
        
        // Avvia l'animazione del titolo
        // const titleInterval = L33tPrompt.print('\nL33T Server Control Panel\n');
        console.log(gradient.pastel.multiline('\nL33T Server Control Panel\n'));


        // Aspetta un momento per far vedere l'animazione
        await new Promise(resolve => setTimeout(resolve, 500));

         // Get plugin menu items
         const pluginMenuItems = pluginManager.getLoadedPlugins()
         .flatMap(plugin => plugin.getMenuItems())
         .map(item => ({ name: item.name, value: item.value }));



        const choice = await L33tPrompt.select('Select an option:', [
            { name: 'System Status', value: 'status' },
            { name: 'Socket.IO Monitor', value: 'socketio' },
            { name: 'Active Connections', value: 'connections' },
            { name: 'Plugin Management', value: 'plugins' },
            { name: 'View Logs', value: 'logs' },
            { name: 'Server Configuration', value: 'config' },
            ...pluginMenuItems, // Add plugin menu items here
            { name: 'Exit', value: 'exit' }
        ]);

        // Ferma l'animazione
        // clearInterval(titleInterval);

        // Pulisci lo schermo prima di mostrare la prossima schermata
        process.stdout.write('\x1Bc');

        const pluginItem = pluginManager.getLoadedPlugins()
            .flatMap(plugin => plugin.getMenuItems())
            .find(item => item.value === choice);

        if (pluginItem) {
            await pluginItem.handler();
            continue;
        }

        switch (choice) {
            case 'status':
                await showSystemStatus();
                break;
            case 'socketio':
                await showSocketIOMonitor();
                break;
            case 'connections':
                console.log('\nNot implemented yet');
                await L33tPrompt.confirm('Press Enter to continue...');
                break;
            case 'plugins':
                console.log('\nNot implemented yet');
                await L33tPrompt.confirm('Press Enter to continue...');
                break;
            case 'logs':
                console.log('\nNot implemented yet');
                await L33tPrompt.confirm('Press Enter to continue...');
                break;
            case 'config':
                console.log('\nNot implemented yet');
                await L33tPrompt.confirm('Press Enter to continue...');
                break;
            case 'exit':
                console.log(gradient.rainbow('\nShutting down L33T Server...'));
                await shutdown();
                return; // Esce dal loop del menu
        }
    }
}

async function showSystemStatus() {
    console.log(gradient.pastel.multiline('\nSystem Status\n'));
    console.log('Server Uptime: 12:34:56');
    console.log('Memory Usage: 234MB');
    console.log('Active Plugins: 3');
    console.log(`Connected Clients: ${io.sockets.sockets.size}`);
    await L33tPrompt.confirm('\nPress Enter to continue...');
}

async function showSocketIOMonitor() {
    while (true) {
        process.stdout.write('\x1Bc');
        console.log(gradient.pastel.multiline('\nSocket.IO Monitor\n'));

        // Ottieni tutte le stanze
        const rooms = io.sockets.adapter.rooms;
        const sockets = await io.fetchSockets();

        // Statistiche generali
        console.log('General Statistics:');
        console.log('-----------------');
        console.log(`Total Connected Clients: ${sockets.length}`);
        console.log(`Total Rooms: ${rooms.size}`);
        console.log('\n');

        // Dettagli dei client connessi
        console.log('Connected Clients:');
        console.log('-----------------');
        for (const socket of sockets) {
            console.log(`Socket ID: ${socket.id}`);
            console.log(`  Rooms: ${Array.from(socket.rooms).join(', ')}`);
            console.log(`  Handshake Time: ${new Date(socket.handshake.time).toLocaleString()}`);
            console.log(`  Client IP: ${socket.handshake.address}`);
            console.log('-----------------');
        }

        // Menu opzioni
        const monitorChoice = await L33tPrompt.select('Select an option:', [
            { name: 'Refresh', value: 'refresh' },
            { name: 'Disconnect All Clients', value: 'disconnect' },
            { name: 'Back to Main Menu', value: 'back' }
        ]);

        switch (monitorChoice) {
            case 'refresh':
                continue;
            case 'disconnect':
                const confirm = await L33tPrompt.confirm('Are you sure you want to disconnect all clients?');
                if (confirm) {
                    sockets.forEach(socket => socket.disconnect(true));
                    console.log('\nAll clients have been disconnected.');
                    await L33tPrompt.confirm('Press Enter to continue...');
                }
                break;
            case 'back':
                return;
        }
    }
}

// Start the server
startServer().then(async () => {


    const mcpServerPing = new MCPServerPing();
    await mcpServerPing.setup(app);  // where app is your Express application


    showMainMenu().catch(error => {
        console.error(`Menu error: ${error}`);
        process.exit(1);
    });
}).catch(error => {
    console.error(`Failed to start server: ${error}`);
    process.exit(1);
});

export { L33tThing, L33tThingTimed };
