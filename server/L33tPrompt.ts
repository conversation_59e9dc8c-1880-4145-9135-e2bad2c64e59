import inquirer from 'inquirer';
import { exit } from 'process';
import gradient from 'gradient-string';

interface SelectOption {
    name: string;
    value: string;
}

export class L33tPrompt {
    static async select(message: string, choices: SelectOption[]): Promise<string> {
        const response = await inquirer.prompt([
            {
                type: 'list',
                name: 'selected',
                message: message,
                choices: choices,
                pageSize: choices.length // Mostra tutte le opzioni senza scroll
            }
        ]);

        return response.selected;
    }

    static async checkbox(message: string, choices: SelectOption[]): Promise<string[]> {
        const response = await inquirer.prompt([
            {
                type: 'checkbox',
                name: 'selected',
                message: message,
                choices: choices
            }
        ]);
        return response.selected;
    }

    static async input(message: string): Promise<string> {
        const response = await inquirer.prompt([
            {
                type: 'input',
                name: 'value',
                message: message
            }
        ]);
        return response.value;
    }

    static async confirm(message: string, defaultValue = false): Promise<boolean> {
        const response = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirmed',
                message: message,
                default: defaultValue
            }
        ]);
        return response.confirmed;
    }

    static animatedPrint(text: string) {
        // Salva la posizione corrente del cursore
        process.stdout.write('\x1b[s');
        
        const colors = [
            ['#00ff00', '#00ffff'],  // green to cyan
            ['#00ffff', '#ff00ff'],  // cyan to magenta
            ['#ff00ff', '#ff0000'],  // magenta to red
            ['#ff0000', '#ffff00'],  // red to yellow
            ['#ffff00', '#00ff00']   // yellow to green
        ];
        
        let colorIndex = 0;
        
        // Prima stampa
        console.log(gradient(colors[colorIndex]).multiline(text));
        
        const intervalId = setInterval(() => {
            // Ripristina la posizione salvata del cursore
            process.stdout.write('\x1b[u');
            // Stampa il nuovo testo colorato
            console.log(gradient(colors[colorIndex]).multiline(text));
            colorIndex = (colorIndex + 1) % colors.length;
        }, 200);

        // Sposta il cursore dopo il testo
        const lines = text.split('\n').length;
        for (let i = 0; i < lines; i++) {
            process.stdout.write('\n');
        }

        return intervalId;
    }
}
