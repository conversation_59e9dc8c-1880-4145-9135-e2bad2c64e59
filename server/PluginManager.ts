import { readdirSync } from 'fs';
import { join, dirname } from 'path';
import { Server as SocketServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import express from 'express';
import { Plugin } from './types/Plugin';
import { fileURLToPath, pathToFileURL } from 'url';

export class PluginManager {
    private plugins: Plugin[] = [];

    constructor(
        private pluginsDir: string,
        private app: express.Application,
        private server: HttpServer,
        private io: SocketServer
    ) {
        // Convert URL back to filesystem path if it's a URL
        if (pluginsDir.startsWith('file://')) {
            this.pluginsDir = fileURLToPath(pluginsDir);
        }
    }

    async loadPlugins(): Promise<void> {
        try {
            const files = readdirSync(this.pluginsDir)
                .filter(file => file.endsWith('.plugin.ts') || file.endsWith('.plugin.js'));

            for (const file of files) {
                try {
                    const pluginPath = join(this.pluginsDir, file);
                    // Convert the path to a proper file URL for ESM import
                    const pluginUrl = pathToFileURL(pluginPath).href;
                    const pluginModule = await import(pluginUrl);
                    
                    if (pluginModule.default && typeof pluginModule.default === 'function') {
                        const plugin: Plugin = new pluginModule.default();
                        
                        console.log(`Loading plugin: ${plugin.name} v${plugin.version}`);
                        await plugin.init(this.app, this.server, this.io);
                        
                        this.plugins.push(plugin);
                        console.log(`Plugin ${plugin.name} loaded successfully`);
                    }
                } catch (error) {
                    console.error(`Failed to load plugin ${file}:`, error);
                }
            }
        } catch (error) {
            console.error('Error loading plugins:', error);
        }
    }

    getLoadedPlugins(): Plugin[] {
        return this.plugins;
    }
}
