// File: socket-client-test.js
const { io } = require("socket.io-client");

const serverUrl = "http://localhost:3000";
const socket = io(serverUrl, {
  transports: ['websocket'],
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
});

const messages = [
  "Hello from the client!",
  "Random message incoming!",
  "Socket.IO is awesome!",
  "Hope you're having a great day!",
  "Another random message for you!",
];

function getRandomMessage() {
  return messages[Math.floor(Math.random() * messages.length)];
}

let messageInterval;

function startSendingMessages() {
  messageInterval = setInterval(() => {
    const message = getRandomMessage();
    console.log("Sending message:", message);
    socket.emit("message", message);
  }, 3000);
}

function stopSendingMessages() {
  if (messageInterval) {
    clearInterval(messageInterval);
    messageInterval = null;
  }
}

socket.on("connect", () => {
  console.log("Connected to server");
  startSendingMessages();
});

socket.on("disconnect", (reason) => {
  console.log(`Disconnected: ${reason}`);
  stopSendingMessages();
});

socket.on("connect_error", (error) => {
  console.error("Connection error:", error.message);
});

socket.on("message", (message) => {
  console.log("Received message:", message);
});

console.log("Starting Socket.IO client test...");

// Stop the test after 1 minute
setTimeout(() => {
  stopSendingMessages();
  socket.disconnect();
  console.log("Socket.IO client test stopped.");
  process.exit(0);
}, 60000);