import React, { useEffect, useState } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { L33tTask } from 'remixed/model/task';
import { javascript } from '@codemirror/lang-javascript';
import { L33tTimed } from 'remixed/model/timed';


// interface TaskCodeEditorProps {
//   source: L33tTask;
//   className?: string;
// }

interface TimedStausProps {
    timed: L33tTimed|undefined;
    className?: string;
}

const TimedStatus: React.FC<TimedStausProps> = ({ timed, className }) => {
    
  if (!timed) {
    return (
        <div className={`flex ${className}`}>
            <div className="rounded p-1 flex items-center bg-gray-400 text-white">
                <i className="fa fa-clock me-2"></i>
                <small>--</small>
            </div>
        </div>
    );
  } 
  
  return ( 

        <div className={`flex ${className}`}>
          {(timed.timedLast - timed.timedPrevious) < timed.timedTTL && (
            <div className="rounded p-1 flex items-center bg-lime-600 text-white">
              <i className="fa fa-clock me-2"></i>
              <small>{timed.timedLast - timed.timedPrevious} ms</small>
            </div>
          )}
          {(timed.timedLast - timed.timedPrevious) >= timed.timedTTL && (
            <div className="rounded p-1 flex items-center bg-red-600 text-white">
              <i className="fa fa-clock me-2"></i>
                 <small>{timed.timedLast - timed.timedPrevious} ms</small>
                {/* {timed.timedLast - timed.timedPrevious} of {timed.timedTTL}ms */}
            </div>
          )}
        </div>
    )
};

export default TimedStatus;