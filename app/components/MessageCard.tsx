import { Link } from "@remix-run/react";
import { L33tAgent } from "l33t-lib";

export const MessageCard = ({ agent }: { agent: L33tAgent }) => (
  <Link 
    to={`${agent?.name}`} 
  >
    <div className="p-4 shadow-md text-white">
      <img
        src={agent.lastMessage?.image}
        alt={`Latest detection result for ID ${agent.name}`}
        className="w-full h-auto mb-2 rounded"
      />
      <h1 className="text-lg font-semibold">{agent.name}</h1>
      
      {/* <p>Time: {agent.lastMessage?.time?.toFixed(4) ?? 'N/A'}s</p> */}
      {/* <p>Memory usage: {agent.lastMessage?.memory_usage?.toFixed(2) ?? 'N/A'} MB</p> */}
      {/* <h3 className="font-semibold mt-2">Detections:</h3>
      <ul className="list-disc list-inside">
        {agent.lastMessage?.detections?.map((detection, index) => (
          <li key={`${agent.id}-detection-${index}`} className="text-xs">
            {detection.class} ({(detection.confidence * 100).toFixed(2)}%)
          </li>
        )) ?? <li>No detections available</li>}
      </ul> */}
    </div>
  </Link>
);


