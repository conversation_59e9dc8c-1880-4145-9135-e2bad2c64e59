import React from 'react';
import { L33tAgent, Shape, Point, ShapeType } from 'remixed/model/l33t';
import { Command, CommandContext } from '.';

export class ClosePolygonCommand implements Command {
  private oldShapes: { [id: string]: Shape } | null = null;
  private oldCurrentShape: Shape | null = null;
  private oldSelectedPoint: { shapeId: string; pointIndex: number } | null = null;
  private closedShape: Shape | null = null;

  constructor(
    private context: CommandContext,
    private currentShape?: Shape 
  ) {}

  execute() {
    const { shapes, setShapes, setCurrentShape, setSelectedPoint } = this.context;

    if (this.currentShape && this.currentShape.points.length > 2) {
      this.oldShapes = { ...shapes };
      this.oldCurrentShape = this.currentShape;
      this.oldSelectedPoint = shapes[this.currentShape.id]
        ? { shapeId: this.currentShape.id, pointIndex: this.currentShape.points.length - 1 }
        : null;

      // Simply mark the shape as closed without adding the first point again
      this.closedShape = {
        ...this.currentShape,
        isClosed: true,
        points: [...this.currentShape.points] // Keep original points without duplication
      };

      setShapes(prevShapes => ({
        ...prevShapes,
        [this.closedShape!.id]: this.closedShape!
      }));
      
      setCurrentShape(null);
      setSelectedPoint(null);
    }
  }

  undo() {
    const { setShapes, setCurrentShape, setSelectedPoint } = this.context;

    if (this.oldShapes) {
      setShapes(this.oldShapes);
      setCurrentShape(this.oldCurrentShape);
      setSelectedPoint(this.oldSelectedPoint);
    }
  }
}