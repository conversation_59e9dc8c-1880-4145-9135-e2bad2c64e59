import React from 'react';
import { Shape, Point } from 'remixed/model/l33t';
import { Command, CommandContext } from '.';

export class InsertPointInShapeCommand implements Command {
  private oldShapes: { [id: string]: Shape } | null = null;
  private oldSelectedPoint: { shapeId: string; pointIndex: number } | null = null;

  constructor(
    private context: CommandContext,
    private shapeId: string,
    private index: number,
    private point: Point,
  ) {}

  execute() {
    const { shapes, setShapes, setSelectedPoint } = this.context;

    this.oldShapes = { ...shapes };
    this.oldSelectedPoint = shapes[this.shapeId]
      ? { shapeId: this.shapeId, pointIndex: this.index - 1 }
      : null;

    setShapes(prevShapes => {
      const updatedShape = prevShapes[this.shapeId];
      if (updatedShape) {
        const newPoints = [...updatedShape.points];
        newPoints.splice(this.index, 0, this.point);
        return {
          ...prevShapes,
          [this.shapeId]: { ...updatedShape, points: newPoints }
        };
      }
      return prevShapes;
    });

    setSelectedPoint({ shapeId: this.shapeId, pointIndex: this.index });
  }

  undo() {
    const { setShapes, setSelectedPoint } = this.context;

    if (this.oldShapes) {
      setShapes(this.oldShapes);
      setSelectedPoint(this.oldSelectedPoint);
    }
  }
}