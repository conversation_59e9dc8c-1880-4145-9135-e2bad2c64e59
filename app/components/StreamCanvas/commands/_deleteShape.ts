import React from 'react';
import { Shape, Point, ShapeType } from 'remixed/model/l33t';
import { Command, CommandContext } from '.';

export class DeleteShapeCommand implements Command {
  private deletedShape: Shape | null = null;
  private oldShapes: { [id: string]: Shape } | null = null;

  constructor(
    private context: CommandContext,
    private selectedShape: Shape | null
  ) {}

  execute(): void {
    const { shapes, setShapes, setSelectedShape, setSelectedPoint } = this.context;
    if (!this.selectedShape) {
      console.log("No shape selected for deletion");
      return;
    }

    this.oldShapes = { ...shapes };
    this.deletedShape = this.selectedShape;

    // Immediately update local state
    const updatedShapes = { ...shapes };
    delete updatedShapes[this.selectedShape.id];
    setShapes(updatedShapes);
    setSelectedShape(null);
    setSelectedPoint(null);
  }

  undo(): void {
    const { setShapes, setSelectedShape } = this.context;

    console.log("Undoing DeleteShapeCommand");

    if (this.oldShapes && this.deletedShape) {
      setShapes(this.oldShapes);
      setSelectedShape(this.deletedShape);
    } else {
      console.log("Unable to undo: No old shapes or deleted shape available");
    }
  }
}