import { Shape, Point, ShapeType } from 'l33t-lib';
import { AddPointCommand } from './_addPoint';
import { ClosePolygonCommand } from './_closePolygon';
import { DeleteShapeCommand } from './_deleteShape';
import { DeleteVertexCommand } from './_deleteVertext';
import { InsertPointInShapeCommand } from './_insertPointShape';
import { DragPointCommand } from './_dragPoint';
import { DragShapeCommand } from './_dragShape';

export interface Command {
    execute(): void;
    undo(): void;
}

export interface CommandContext {
  shapes: { [id: string]: Shape };
  setShapes: React.Dispatch<React.SetStateAction<{ [id: string]: Shape }>>;
  setCurrentShape: React.Dispatch<React.SetStateAction<Shape | null>>;
  setSelectedPoint: React.Dispatch<React.SetStateAction<{ shapeId: string; pointIndex: number } | null>>;
  setSelectedShape: React.Dispatch<React.SetStateAction<Shape | null>>;
  selectedShape: Shape | null;
  selectedPoint: { shapeId: string; pointIndex: number } | null;
}


export function createShapeCommand(
    commandType: 'AddPoint' | 'ClosePolygon' | 'DeleteShape' | 'DeleteVertex' | 'InsertPointInShape' | 'DragPoint' | 'DragShape',
    context: CommandContext,
    params: {
      currentShape: Shape | null;
      point?: Point;
      index?: number;
      shapeId?: string; 
      dx?: number;
      dy?: number;
      canvasWidth?: number;
      canvasHeight?: number;
      drawMode?: ShapeType;
      selectedPoint?: { shapeId: string; pointIndex: number } | null;
    }
  ): Command {

    switch (commandType) {

      case 'AddPoint':
        if (!params.point || !params.drawMode) 
          throw new Error('Invalid parameters for AddPoint command');
        return new AddPointCommand(context, params.point, params.drawMode, params.currentShape);
      
      case 'ClosePolygon':
        if (!params.currentShape) 
          throw new Error('Invalid parameters for ClosePolygon command');
        return new ClosePolygonCommand(context ,params.currentShape );
      
      case 'DeleteShape':
        return new DeleteShapeCommand(context, params.currentShape);
      
      case 'DeleteVertex':
        if (!params.selectedPoint) 
          throw new Error('Invalid parameters for DeleteVertex command');
        return new DeleteVertexCommand(context, params.selectedPoint);
      
      case 'InsertPointInShape':
        if (!params.point || !params.index || !params.shapeId) 
          throw new Error('Invalid parameters for InsertPointInShape command');
        return new InsertPointInShapeCommand(context, params.shapeId, params.index, params.point);
      
      case 'DragPoint':
        if (!params.selectedPoint || !params.currentShape || params.dx === undefined || params.dy === undefined) 
          throw new Error('Invalid parameters for DragPoint command');

        return new DragPointCommand(context, params.selectedPoint, params.currentShape, params.dx, params.dy);
      
      case 'DragShape':
        if (!params.shapeId || params.dx === undefined || params.dy === undefined) 
          throw new Error('Invalid parameters for DragShape command');

        return new DragShapeCommand(context, params.shapeId, params.dx, params.dy, params.canvasWidth!, params.canvasHeight!);
        
      default:
        throw new Error(`Unknown command type: ${commandType}`);
    }
  }


