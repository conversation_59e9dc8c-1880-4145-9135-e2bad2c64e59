import {
  <PERSON>,
  <PERSON>s,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON>ts,
  ScrollRestoration,
  useLoaderData,
  useLocation,
} from "@remix-run/react";
import { json, LoaderFunction } from "@remix-run/node";
import { useEffect, useState } from "react";
import type { Socket } from "socket.io-client";
import io from "socket.io-client";
import stylesheet from "./tailwind.css?url";
import { SocketProvider } from "./context";
import { l33tSerializer } from "../server/misc";
import { L33tMemDB } from "l33t-lib";

export const links = () => [
  { rel: "stylesheet", href: stylesheet },
];

export const loader: LoaderFunction = async ({ context }) => {
  return json(l33tSerializer(context));
};

export default function App() {
  const { memoryDB } = useLoaderData<{ memoryDB: L33tMemDB }>();


  const [socket, setSocket] = useState<Socket | undefined>();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showCheck, setShowCheck] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const location = useLocation();

  useEffect(() => {
    // Resetta lo stato quando cambia il percorso
    setShowCheck(false);
    setSelectedIds([]);
  }, [location.pathname]);

  useEffect(() => {
    const newSocket = io();
    setSocket(newSocket);
    return () => {
      newSocket.close();
    };
  }, []);

  const toggleSelection = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]
    );
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handlePersist = () => {
    if (socket) {
      socket.emit("l33t_db_persist");
      setIsDropdownOpen(false);
    }
  };

  const handleBulkAction = (action: "start" | "stop" | "delete") => {
    if (!socket || selectedIds.length === 0) return;

    const entityType = location.pathname.startsWith("/streams")
      ? "streams"
      : location.pathname.startsWith("/plcs")
      ? "PLCs"
      : "tasks";

    const confirmMessage = `Are you sure you want to ${action} ${selectedIds.length} ${entityType}?`;

    if (window.confirm(confirmMessage)) {
      selectedIds.forEach((id) => {
        switch (location.pathname) {
          case "/tasks":
            switch (action) {
              case "start":
                socket.emit("l33t_task_start", { taskId: id });
                break;
              case "stop":
                console.log("emit stop")
                socket.emit("l33t_task_stop", { taskId: id });
                break;
              case "delete":
                socket.emit("l33t_delete_task", { taskId: id });
                break;
            }
            break;

          case "/streams":
            if (action === "delete") {
              socket.emit("l33t_delete_agent", { agentId: id });
            }
            break;

          case "/plcs":
            if (action === "delete") {
              socket.emit("l33t_delete_plc", { plcId: id });
            }
            break;
        }
      });

      setSelectedIds([]);
      setShowCheck(false);
    }
  };





  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
        <script src="https://kit.fontawesome.com/aa20b18152.js"></script>
      </head>
      <body className="animate bg-black bg-gradient-to-r from-[rgba(49,46,129,1)] to-[rgba(0,0,0,1)]">
        <div className="area">
          <ul className="circles">
            {[...Array(10)].map((_, i) => (
              <li key={i}></li>
            ))}
          </ul>
        </div>

        <div className="from-[rgba(49,46,129,0.1)] to-[rgba(0,0,0,0.2)] bg-gradient-to-bl">
          <ul className="items-center container mx-auto flex flex-wrap mb-px text-sm text-center text-white-500 dark:text-gray-400">
            <li className="p-4 group relative">
              <button
                onClick={toggleDropdown}
                className="text-xl text-lime-500 hover:text-lime-300 focus:outline-none"
              >
                <i className="fa-duotone fa-lg fa-solid fa-hat-witch me-2"></i>
              </button>
              {isDropdownOpen && (
                <div className="absolute left-0 mt-2 w-48 bg-gray-900 rounded-md shadow-lg py-1 z-10">
                  <button
                    onClick={handlePersist}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                  >
                    Persist
                  </button>
                  <button
                    className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                  >
                    Restore
                  </button>
                </div>
              )}
            </li>

            <li className="me-2">
              <Link
                to="/streams"
                className="text-gray-400 inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-100 hover:border-gray-300 group"
              >
                <i className="fa-duotone fa-xl fa-solid fa-camera-security me-2"></i>
                Streams
              </Link>
            </li>

            <li className="me-2">
              <Link
                to="/plcs"
                className="text-gray-400 inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-100 hover:border-gray-300 group"
              >
                <i className="fa-duotone fa-xl fa-solid fa-microchip me-2"></i>
                PLCs
              </Link>
            </li>

            <li className="me-2">
              <Link
                to="/tasks"
                className="text-gray-400 inline-flex items-center justify-center p-4 border-b-2 border-transparent hover:text-gray-100 hover:border-gray-300 group"
              >
                <i className="fa-duotone fa-xl fa-solid fa-play me-2"></i>
                Tasks
              </Link>
            </li>

            {/* Buttons aligned to the right, only on tasks page */}
            <div className="ml-auto flex items-center">
              {selectedIds.length > 0 && (
                <>
                  {location.pathname.startsWith('/tasks') && (
                    <>
                      <li>
                        <button
                          className="text-gray-400 inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-green-600 hover:border-green-300 group"
                          onClick={() => handleBulkAction('start')}
                        >
                          <i className="fa-duotone fa-xl fa-solid fa-play me-2"></i>
                          Start ({selectedIds.length})
                        </button>
                      </li>
                      <li>
                        <button
                          className="text-gray-400 inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-yellow-600 hover:border-yellow-300 group"
                          onClick={() => handleBulkAction('stop')}
                        >
                          <i className="fa-duotone fa-xl fa-solid fa-stop me-2"></i>
                          Stop ({selectedIds.length})
                        </button>
                      </li>
                    </>
                  )}
                  <li>
                    <button
                      className="text-gray-400 inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-red-600 hover:border-red-300 group"
                      onClick={() => handleBulkAction('delete')}
                    >
                      <i className="fa-duotone fa-xl fa-solid fa-trash me-2"></i>
                      Delete ({selectedIds.length})
                    </button>
                  </li>
                </>
              )}
            </div>

          </ul>
        </div>

        <div className="flex flex-col min-h-screen">
          <SocketProvider socket={socket}>
            <Outlet context={{ memoryDB, showCheck, toggleSelection, selectedIds }} />
          </SocketProvider>
        </div>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}