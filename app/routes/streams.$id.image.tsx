import { usePara<PERSON>, useNavigate, useOutlet<PERSON>ontext, Link, useLoaderData } from "@remix-run/react";
import { useState, useEffect } from "react";
import { useSocket } from "../context";
import { loaderAgentById } from "../loaders";


// l33t loader
export const loader = loaderAgentById

export default function StreamDetail() {
  const navigate = useNavigate();
  const socket = useSocket();
  const [selectedAgent, setSelectedAgent] = useState(useLoaderData<typeof loader>());
  const [formData, setFormData] = useState(selectedAgent?.config ? selectedAgent.config : {});
  const { id } = useParams<{ id: string }>();

  // useEffect(() => {
  //   setFormData(selectedAgent?.config ? selectedAgent.config : {})
  // }, [selectedAgent])


  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") navigate("..");
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [navigate]);

  if (!selectedAgent) return null; // or a loading state if preferred

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseFloat(value),
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    socket?.emit("l33t_update_config", formData, id);

  };

  return (
    <div 
      className="">

       <form onSubmit={handleSubmit} className=" pt-6 pb-8 mb-4">

                <div className="mb-4 md:flex md:flex-wrap md:-mx-2">
                  <InputWithSlider
                    label="Zoom"
                    name="zoom"
                    value={formData.zoom ? formData.zoom : 0}
                    onChange={handleChange}
                    min={1}
                    max={10}
                    step={0.1}
                  />
                  <InputWithSlider
                    label="Rotation Angle"
                    name="rotation_angle"
                    value={formData.rotation_angle ? formData.rotation_angle : 0}
                    onChange={handleChange}
                    min={0}
                    max={360}
                  />
                  <InputWithSlider
                    label="Crop X"
                    name="crop_x"
                    value={formData.crop_x ? formData.crop_x : 0}
                    onChange={handleChange}
                    min={0}
                    max={100}
                  />
                  <InputWithSlider
                    label="Crop Y"
                    name="crop_y"
                    value={formData.crop_y ? formData.crop_y :0}
                    onChange={handleChange}
                    min={0}
                    max={100}
                  />
                  <InputWithSlider
                    label="Crop Width"
                    name="crop_width"
                    value={formData.crop_width ? formData.crop_width : 0}
                    onChange={handleChange}
                    min={0}
                    max={100}
                  />
                  <InputWithSlider
                    label="Crop Height"
                    name="crop_height"
                    value={formData.crop_height ? formData.crop_height : 0}
                    onChange={handleChange}
                    min={0}
                    max={100}
                  />
                  </div>

                

              <div className="flex w-full  gap-5	">
                <button onClick={() => navigate("..")} className="grow mt-5 bg-transparent hover:bg-indigo-500 text-white-800 font-semibold py-2 px-4 border border-indigo-900 rounded ">
                  Cancel
                </button>

                <button type="submit" className="grow mt-5 bg-indigo-900 text-white hover:bg-indigo-500 text-gray-800 font-semibold py-2 px-4 rounded">
                  Apply
                </button>
              </div>
            </form> 
    </div>
  );
}
interface InputWithSliderProps {
  label: string;
  name: keyof FormData;
  value: number;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  min: number;
  max: number;
  step?: number;
}

interface FormData {
  zoom: number;
  rotation_angle: number;
  crop_x: number;
  crop_y: number;
  crop_width: number;
  crop_height: number;
}


const InputWithSlider: React.FC<InputWithSliderProps> = ({
  label,
  name,
  value,
  onChange,
  min,
  max,
  step = 1
}) => (
  <div className="md:w-1/2 md:px-2 mb-4">
    <label className="block text-white text-sm font-bold mb-2" htmlFor={name}>
      {label}
    </label>
    <div className="flex items-center">
      <input
        className="shadow appearance-none bg-transparent border border-gray-600 rounded w-1/3 py-2 px-3 text-white leading-tight focus:outline-none focus:shadow-outline focus:border-indigo-500 active:border-indigo-500 mr-2"
        id={name}
        type="number"
        name={name}
        value={value}
        onChange={onChange}
        min={min}
        max={max}
        step={step}
      />
      <input
        className="w-2/3 accent-indigo-500"
        type="range"
        name={name}
        value={value}
        onChange={onChange}
        min={min}
        max={max}
        step={step}
      />
    </div>
  </div>
);


