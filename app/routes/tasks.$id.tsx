import { useEffect, useState, useCallback } from "react";
import { useLoaderData, useNavigate, Link, Outlet, useParams, useLocation } from "@remix-run/react";
import { useSocket } from "../context";
import { loaderTaskById } from "../loaders";
import { L33tConfig } from "~/l33t-lib";

export const loader = loaderTaskById;

export default function TaskDetail() {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const selectedTask = useLoaderData<typeof loader>();
  const [width, setWidth] = useState(600);
  const [isDragging, setIsDragging] = useState(false);
  const socket = useSocket();

  const handleSaveSettings = useCallback((config: L33tConfig) => {
    if (!id) return;
    if (socket) {
      socket.emit("l33t_update_config", config, id);
    }
  }, [socket, id]);

  // Navigate to code tab by default if no specific route is selected
  useEffect(() => {
    if (location.pathname.endsWith(`/${id}`)) {
      navigate('code', { replace: true });
    }
  }, [location, id, navigate]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  }, []);

  useEffect(() => {
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const newWidth = window.innerWidth - e.clientX;
        setWidth(Math.max(400, Math.min(newWidth, window.innerWidth - 100)));
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        navigate("..");
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [navigate]);

  if (!selectedTask) return "Nothing here";

  return (
    <div
      className="fixed top-0 right-0 flex min-h-full h-full bg-black bg-opacity-70 drop-shadow-2xl text-white"
      style={{ width: `${width}px` }}
    >
      {/* Resize Handle */}
      <div
        className="absolute left-0 top-0 w-1 h-full cursor-col-resize bg-transparent hover:bg-indigo-500 transition-colors"
        onMouseDown={handleMouseDown}
      />

      <div className="flex flex-col w-full h-full">
        <div className="p-5">
          <div className="flex items-center justify-between mb-2">
            <h1 className="font-bold flex items-center gap-2 text-2xl">
             {id}
              <div className={`w-2.5 h-2.5 rounded-full ${!(selectedTask.isStarted) ? 'bg-red-500' : 'bg-green-500'}`} />
            </h1>

            <ul className="flex text-sm text-center text-white-500">
              <li className="me-2">
                <Link
                  to="code"
                  className={`inline-flex items-center justify-center p-4 border-b-2 rounded-t-lg group
                    ${location.pathname.includes('/code')
                      ? 'text-gray-100 border-gray-300'
                      : 'text-gray-400 border-transparent hover:text-gray-100 hover:border-gray-300'}`}
                >
                  <i className="fa-light fa-xl fa-code me-2"></i>Code
                </Link>
              </li>
              <li className="me-2">
                <Link
                  to="settings"
                  className={`inline-flex items-center justify-center p-4 border-b-2 rounded-t-lg group
                    ${location.pathname.includes('/settings')
                      ? 'text-gray-100 border-gray-300'
                      : 'text-gray-400 border-transparent hover:text-gray-100 hover:border-gray-300'}`}
                >
                  <i className="fa-light fa-xl fa-gear me-2"></i>Settings
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Content area - takes remaining height */}
        <div className="flex-1 min-h-0">
          <Outlet context={{ selectedTask, onSaveSettings: handleSaveSettings }} />
        </div>
      </div>
    </div>
  );
}