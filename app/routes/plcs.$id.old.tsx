import React, { useState, useEffect } from 'react';
import { useLoaderData, useNavigate, useOutletContext, useParams } from "@remix-run/react";
import { useSocket } from "../context";
import { L33tADSPLC } from "@l33t-lib";
import { loaderPLCById } from '../loaders';

/* Importazioni originali commentate
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faChevronRight, faChevronDown, faLink, faUnlink, faFilter } from '@fortawesome/free-solid-svg-icons';
import { Tree, NodeRendererProps } from 'react-arborist';
import ValueOscilloscope, { DataPoint } from '../components/ValueOscilloscope';
*/

export const loader = loaderPLCById;

export default function PlcDetail() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [selectedPLC] = useState<L33tADSPLC>(useLoaderData<typeof loader>());
  const socket = useSocket();

  // Stati originali commentati
  /*
  const [search, setSearch] = useState('');
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  const [varValue, setVarValue] = useState<DataPoint|undefined>();
  const [showOnlyBound, setShowOnlyBound] = useState(false);
  const [symbols, setSymbols] = useState<any>(null);
  */
  
  const [boundVariables, setBoundVariables] = useState<string[]>(
    selectedPLC ? selectedPLC.boundVariables : []
  );

  // Navigazione con ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") navigate("..");
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [navigate]);

  // Handler per aggiornamenti
  const handleChange = (field: string, value: string) => {
    const updatedPLC = { ...selectedPLC, [field]: value };
    
    if (socket) {
      socket.emit("l33t_plc_update", id, updatedPLC);
    }
  };

  if (!id) return <div>Error: No PLC ID provided</div>;
  if (!selectedPLC) return <div>No PLC selected</div>;

  return (
    <div className="fixed top-0 right-0 w-1/3 h-full bg-gray-800 text-white p-5">
      <h2 className="text-xl font-bold mb-6">PLC Details</h2>
      
      <form className="space-y-4">
        {/* ID Field */}
        <div>
          <label className="block text-sm font-medium mb-1">ID</label>
          <input 
            type="text"
            value={selectedPLC.id}
            onChange={(e) => handleChange('id', e.target.value)}
            className="w-full bg-gray-700 rounded px-3 py-2"
          />
        </div>

        {/* IP Field */}
        <div>
          <label className="block text-sm font-medium mb-1">IP Address</label>
          <input 
            type="text"
            value={selectedPLC.ip}
            onChange={(e) => handleChange('ip', e.target.value)}
            className="w-full bg-gray-700 rounded px-3 py-2"
          />
        </div>
      </form>

      {/* Bound Variables List */}
      {boundVariables && boundVariables.length > 0 ? (
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-3">Bound Variables</h3>
          <ul className="space-y-2">
            {boundVariables.map((variable, index) => (
              <li 
                key={index}
                className="bg-gray-700 px-3 py-2 rounded"
              >
                {variable}
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <p className="mt-8 text-gray-400">No bound variables</p>
      )}
    </div>
  );
}

/* Componenti originali commentati
interface TreeNode {
  id: string;
  name: string;
  children?: TreeNode[];
  type: 'program' | 'variable';
  path: string;
  isBound?: boolean;
}

const Node: React.FC<NodeRendererProps<TreeNode> & { 
  onSelect: (node: TreeNode) => void;
  onToggleBind: (node: TreeNode) => void;
}> = ... 

const SymbolDisplay = ({ symbol }: { symbol: any }) => ...
*/