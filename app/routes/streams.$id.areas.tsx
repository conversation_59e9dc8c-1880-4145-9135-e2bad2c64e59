import { j<PERSON>, <PERSON>aderFunction } from "@remix-run/node";
import { useLoaderData, useOutletContext, useParams } from "@remix-run/react";
import { useState, useEffect, useCallback } from "react";
import { useSocket } from "../context";
import { L33tAgent, Shape } from "l33t-lib";
import StreamCanvas from "../components/StreamCanvas/StreamCanvas";
import { loaderAgentById } from "../loaders";

// l33t loader
export const loader = loaderAgentById

export default function StreamArea() {
  const selectedAgent:L33tAgent = useLoaderData<typeof loader>();

  const socket = useSocket();
  const [shapes, setShapes] = useState<Record<string,[]>>(selectedAgent.shapes);
  const { id } = useParams<{ id: string }>();
  useEffect(() => {
    // Check if selectedAgent exists and has a shapes property
    // if (selectedAgent.shapes)
    //   setShapes(selectedAgent.shapes);
    // const agentShapes = Object.values(selectedAgent.shapes);
    // if (agentShapes) {
    //   console.log("Updating shapes from agent:", agentShapes);
    // }
  }, [selectedAgent]);

  useEffect(() => {
    
    if (!socket) return;

    const handleL33tUpdate = (id:string, updatedAgent: L33tAgent) => {
      console.log("xxxeff", updatedAgent)

      // Get the current agent's ID from the first entry in the record
      const currentAgentId = id;
      if (id === currentAgentId ) {
        setShapes(updatedAgent.shapes || []);
      }
    };

    socket.on("l33t_update", handleL33tUpdate);
    return () => {
      socket.off("l33t_update", handleL33tUpdate);
    };
  }, [socket, id]);

  if (!selectedAgent) return <div>Loading...</div>;

  const handleSaveShapes = useCallback((newShapes: Record<string,[]>) => {

    console.log('Saving shapes:', newShapes, "on id", id);

    if (socket && id) {
      socket.emit("l33t_update_shapes", { shapes: newShapes, id: id });
      setShapes(newShapes);  // Update local state
      console.log('Shapes saved:', newShapes);
    } else {
      console.error('Socket not available or agent ID is missing');
    }
  }, [socket, selectedAgent]);

  return (
    <div className="p-4">
      <StreamCanvas 
        backgroundImage={selectedAgent.lastMessage?.image ?? ""} 
        initialShapes={shapes}
        onSave={handleSaveShapes}
      />

   
    </div>
  );
}