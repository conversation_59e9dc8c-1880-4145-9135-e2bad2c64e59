import { LoaderFunctionArgs } from "@remix-run/node";
import { useNavigate, useLoaderData } from "@remix-run/react";
import { useState, useEffect } from "react";
import { useSocket } from "../context";
import { loaderAgentById } from "../loaders";

export const loader = loaderAgentById;

export default function StreamDetections() {
  const navigate = useNavigate();
  const socket = useSocket();
  
  const selectedAgent = useLoaderData<typeof loader>();
  const [formData, setFormData] = useState(selectedAgent?.config || {});
  
  // Initialize detections safely with a default empty array
  const [detections] = useState<any>(selectedAgent?.lastMessage?.detections || []);

  useEffect(() => {
    setFormData(selectedAgent?.config || {});
  }, [selectedAgent]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") navigate("..");
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [navigate]);
  
  if (!selectedAgent) {
    return <div className="text-white">Loading...</div>;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseFloat(value),
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    socket?.emit("l33t_update_config", formData, selectedAgent.id);
  };

  // Display empty state if no image or no detections
  if (!selectedAgent.lastMessage?.image) {
    return (
      <div className="text-center text-white p-4">
        <p>No image available yet</p>
      </div>
    );
  }

  return (
    <div className="">
      <img 
        src={selectedAgent.lastMessage.image}
        alt={`Latest detection result for ID ${selectedAgent.id}`}
        className="w-full h-auto rounded"
      />

      {detections && detections.length > 0 ? (
        <table className="min-w-full divide-y divide-black mt-4">
          <thead className="bg-indgo-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Label
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Confidence
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-black">
            {detections.map((detection: any, index: number) => (
              <tr key={index} className="hover:bg-indigo-700">
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {detection.class}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-600 h-2.5 rounded-full" 
                        style={{ width: `${detection.confidence * 100}%` }}
                      ></div>
                    </div>
                    <span className="ml-2">{(detection.confidence * 100).toFixed(2)}%</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <div className="text-center text-white p-4 mt-4">
          <p>No detections available</p>
        </div>
      )}
    </div>
  );
}