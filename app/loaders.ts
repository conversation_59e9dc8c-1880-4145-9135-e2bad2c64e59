import { json, LoaderFunctionArgs } from "@remix-run/node";
import { L33tMemDB } from "l33t-lib";
import { l33tSerializer } from "../server/misc";

export const loaderAgentById = async ({
    context,
    params
  }: LoaderFunctionArgs) => {
    var x = context.memoryDB as L33tMemDB
    return json(l33tSerializer(x.agents[params.id!]));
  };


export const loaderAgents = async ({
  context,
  params
}: LoaderFunctionArgs) => {
  var x = context.memoryDB as L33tMemDB
  return json(l33tSerializer(x.agents))
};


export const loaderPLCs = async ({
  context,
  params
}: LoaderFunctionArgs) => {
  var x = context.memoryDB as L33tMemDB
  return json(l33tSerializer(x.plcs))
}


export const loaderPLCById = async ({
  context,
  params
}: LoaderFunctionArgs) => {
  var x = context.memoryDB as L33tMemDB
  return json(l33tSerializer(x.plcs[params.id!]))
}


export const loaderTaskById = async ({
  context,
  params
}: LoaderFunctionArgs) => {
  var x = context.memoryDB as L33tMemDB
  return json(l33tSerializer(x.tasks[params.id!]))
}

