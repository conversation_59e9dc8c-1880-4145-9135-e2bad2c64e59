module.exports = { run };

const { faMemory } = require("@fortawesome/free-solid-svg-icons");
const ejs = require("ejs");

async function run(ctx) {
  let travi = 2; // TODO trova travi nell'area UNO dalla telecamere hundeger-out
  // return {
  //   "pronto": true,
  //   "attivo": true
  // }
  // if (travi.lenght >0)
  //   plc.write("PEZZO_PRONTO", true)
  // else
  //   plc.write("PEZZO_PRONTO", false)
  if (ctx.memoryDB.agents) {
    console.log(ctx.getArea("STREAM1", "yvivsfkne").points);
    console.log(ctx.getDetections("STREAM12"));
  }

  const intersectsLineBox = ctx.l33tgeom.lineToBox(
    ctx.three.line1,
    ctx.three.box1
  );
  const intersectsBoxes = ctx.l33tgeom.boxToBox(ctx.three.box1, ctx.three.box2);
  const intersectsLines = ctx.l33tgeom.lineToLine(
    ctx.three.line1,
    ctx.three.line2
  );

  const polygonLineIntersects = ctx.l33tgeom.polygonToLine(
    ctx.three.polygon1,
    ctx.three.line1
  );

  const polygonPolygonIntersects = ctx.l33tgeom.polygonToPolygon(
    ctx.three.polygon1,
    ctx.three.polygon2
  );

  let data = [
    {
      name: "Intersects a line box?",
      value: intersectsLineBox,
    },
    {
      name: "Intersects two boxes?",
      value: intersectsBoxes,
    },
    {
      name: "Intersects two lines?",
      value: intersectsLines ? true : false,
    },
    {
      name: "Intersects polygon lines?",
      value: polygonLineIntersects,
    },
    {
      name: "Intersects polygon to polygon?",
      value: polygonPolygonIntersects,
    },
  ];

  console.log(data);
  var renderedPage = await ejs.renderFile("./task-template.ejs", {
    dataRender: data,
  });

  //console.log(renderedPage)
  return renderedPage;
}
