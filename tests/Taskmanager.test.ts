import { L33tTaskManager } from "~/server/taskmanager.js";
import { L33tMemDB } from "../app/model/l33t-memdb.js";
import { L33tTask } from "../app/model/task.js";
import { Server } from "socket.io";


// Mock dependencies
jest.mock('socket.io');
jest.mock('../app/remixed/memorydb');
jest.mock('fs/promises');
jest.mock('../app/remixed/misc')

describe('L33tTaskManager', () => {
  let taskManager: L33tTaskManager;
  let mockIo: jest.Mocked<Server>;
  let mockL33tMemDB: jest.Mocked<L33tMemDB>;
  // let mockContext: L33tContext;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Setup mocks
    mockIo = new Server() as jest.Mocked<Server>;
    mockL33tMemDB = new L33tMemDB() as jest.Mocked<L33tMemDB>;
    mockContext = new L33tContext();
    
    // Initialize task manager
    taskManager = new L33tTaskManager(mockIo, mockL33tMemDB);

    // Mock readFile
    //(fs.readFile as jest.Mock).mockResolvedValue('export const run = async (context) => { return "test result"; }');
  });

  describe('applyConfig', () => {
    it('should apply new task configuration', async () => {
      const mockConfig = {
        task1: {
          path: '../task1.ts',
          scheduledInterval: 1000
        } as L33tTask
      };

      const result = await taskManager.applyConfig(mockConfig);
      
      expect(result).toBe(true);
      expect(taskManager.getRunningConfig()).toEqual(mockConfig);
    });

    it('should stop existing tasks before applying new config', async () => {
      // First apply some initial config
      const initialConfig = {
        task1: {
          path: '../task1.ts',
          scheduledInterval: 1000,
          _nodeinterval: setInterval(() => {}, 1000)
        } as L33tTask
      };

      await taskManager.applyConfig(initialConfig);

      // Then apply new config
      const newConfig = {
        task2: {
          path: '../task1.ts',
          scheduledInterval: 2000
        } as L33tTask
      };

      const result = await taskManager.applyConfig(newConfig);
      
      expect(result).toBe(true);
      expect(taskManager.getRunningConfig()).toEqual(newConfig);
    });
  });

  describe('startAll', () => {
    it('should start all configured tasks', async () => {
      // Setup test configuration
      const mockConfig = {
        task1: {
          path: '../task1.ts',
          scheduledInterval: 1000
        } as L33tTask
      };

      await taskManager.applyConfig(mockConfig);
      
      // Mock setInterval
      jest.useFakeTimers();
      
      taskManager.startAll(mockContext);

      // Fast-forward time
      jest.advanceTimersByTime(1000);

      // Check if task was executed
      expect(mockIo.emit).toHaveBeenCalledWith(
        'l33t_task_exec',
        expect.any(String)
      );

      // Restore timers
      jest.useRealTimers();
    });
  });

  describe('stopAll', () => {
    it('should stop all running tasks', async () => {
      // Setup test configuration with running tasks
      const mockConfig = {
        task1: {
          path: '../task1.ts',
          scheduledInterval: 1000,
          _nodeinterval: setInterval(() => {}, 1000)
        } as L33tTask
      };

      await taskManager.applyConfig(mockConfig);
      
      // Mock clearInterval
      jest.spyOn(global, 'clearInterval');
      
      taskManager.stopAll();

      expect(clearInterval).toHaveBeenCalled();
      expect(taskManager.getRunningConfig().task1._nodeinterval).toBeNull();
    });
  });

  describe('getRunningConfig', () => {
    it('should return current running configuration', async () => {
      const mockConfig = {
        task1: {
          path: '../task1.ts',
          scheduledInterval: 1000
        } as L33tTask
      };

      await taskManager.applyConfig(mockConfig);
      
      const config = taskManager.getRunningConfig();
      expect(config).toEqual(mockConfig);
    });

    it('should return empty object when no tasks are configured', () => {
      const config = taskManager.getRunningConfig();
      expect(config).toEqual({});
    });
  });
});