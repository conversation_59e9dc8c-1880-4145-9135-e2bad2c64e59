FROM node:22-alpine AS builder

# Exclude dev dependencies
ENV NODE_ENV=production

WORKDIR /app

# Install build essentials
RUN apk add --no-cache python3 make g++

# Copy package files first to leverage cache
COPY package.json yarn.lock ./
COPY l33t-lib/package.json ./l33t-lib/

# Install only production dependencies
RUN yarn install --frozen-lockfile --production=false

# Copy local library and source code
COPY l33t-lib ./l33t-lib
COPY . .

# Create symlink for local library
RUN rm -rf node_modules/l33t-lib && ln -s ../l33t-lib node_modules/l33t-lib

# Clean up build dependencies
RUN apk del python3 make g++

# Remove unnecessary files
RUN rm -rf .git .env *.log

EXPOSE 1337

CMD ["yarn", "dev", "--config", "projects/gali"]