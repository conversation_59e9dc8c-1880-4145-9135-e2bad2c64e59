import { Injectable } from '@angular/core';
import { Socket, io } from 'socket.io-client';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class L33tSocketService {
  public socket: Socket;
  private connected$ = new BehaviorSubject<boolean>(false);

  constructor() {
    this.socket = io("http://localhost:1337", {
      transports: ['websocket'],
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.setupConnectionListeners();
  }

  private setupConnectionListeners(): void {
    this.socket.on('connect', () => {
      console.log('Connected to L33t server');
      this.connected$.next(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log(`Disconnected from L33t server: ${reason}`);
      this.connected$.next(false);
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.connected$.next(false);
    });
  }

  // Connection status
  public isConnected(): Observable<boolean> {
    return this.connected$.asObservable();
  }

  // Tasks
  public addTask(taskId: string, task?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_add_task', taskId, task);
      this.socket.once('l33t_add_task_result', (result) => {
        if (result.success) {
          resolve(result);
        } else {
          reject(result.error);
        }
      });
    });
  }

  public deleteTask(taskId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_delete_task', { taskId });
      this.socket.once('l33t_delete_task_result', (result) => {
        if (result.success) {
          resolve();
        } else {
          reject(result.error);
        }
      });
    });
  }

  public startTask(taskId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_task_start', { taskId });
      this.socket.once('l33t_task_start_result', (result) => {
        if (result.success) {
          resolve();
        } else {
          reject(result.error);
        }
      });
    });
  }

  public stopTask(taskId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_task_stop', { taskId });
      this.socket.once('l33t_task_stop_result', (result) => {
        if (result.success) {
          resolve();
        } else {
          reject(result.error);
        }
      });
    });
  }

  // Agents
  public addAgent(agentId: string, agent?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_add_agent', agentId, agent);
      this.socket.once('l33t_add_agent_result', (result) => {
        if (result.success) {
          resolve(result);
        } else {
          reject(result.error);
        }
      });
    });
  }

  public deleteAgent(agentId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_delete_agent', { agentId });
      this.socket.once('l33t_delete_agent_result', (result) => {
        if (result.success) {
          resolve();
        } else {
          reject(result.error);
        }
      });
    });
  }

  // PLCs
  public addPlc(plcId: string, plc: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.socket.emit('l33t_add_plc', plcId, plc);
      this.socket.once('l33t_add_plc_result', (result) => {
        if (result.success) {
          resolve(result);
        } else {
          reject(result.error);
        }
      });
    });
  }

  public updatePlcBoundVariables(plcId: string, boundVariables: string[]): void {
    this.socket.emit('l33t_ads_update_bound_variables', {
      plcId,
      boundVariables
    });
  }

  // Event listeners
  public onTaskUpdate(): Observable<any> {
    return new Observable(observer => {
      this.socket.on('l33t_task_update', (taskId, taskData) => {
        observer.next({ taskId, taskData });
      });
      return () => this.socket.off('l33t_task_update');
    });
  }

  public onAgentUpdate(): Observable<any> {
    return new Observable(observer => {
      this.socket.on('l33t_agent_update', (agentId, agentData) => {
        observer.next({ agentId, agentData });
      });
      return () => this.socket.off('l33t_agent_update');
    });
  }

  public onPlcUpdate(): Observable<any> {
    return new Observable(observer => {
      this.socket.on('l33t_plc_update', (plcId, plcData) => {
        observer.next({ plcId, plcData });
      });
      return () => this.socket.off('l33t_plc_update');
    });
  }

  public onAdsCallback(): Observable<any> {
    return new Observable(observer => {
      this.socket.on('l33t_ads_callback', (data) => {
        observer.next(data);
      });
      return () => this.socket.off('l33t_ads_callback');
    });
  }

  public onChatError(callback: (data: { error: any }) => void): void {
    this.socket.on('chat_error', callback);
  }

  // Cleanup
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}
