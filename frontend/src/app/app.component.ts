import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { L33tSocketService } from './services/l33t-socket.service';
import { Subscription } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

interface ChatMessage {
  role: string;
  content: string;
  mcpResults?: any[];
  htmlContent?: SafeHtml;
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [RouterOutlet, CommonModule, FormsModule],
  host: {
    class: 'animate bg-black bg-gradient-to-r from-[rgba(49,46,129,1)] to-[rgba(0,0,0,1)]'
  }
})
export class AppComponent implements OnInit, On<PERSON><PERSON>roy {
  isConnected = false;
  // private subscription: Subscription;
  
  messages: ChatMessage[] = [];
  currentMessage = '';
  isTyping = false;
  streamingResponse = '';

  constructor(
    // private socketService: L33tSocketService,
    // private sanitizer: DomSanitizer
  ) {
    // this.subscription = this.socketService.isConnected().subscribe(
    //   connected => this.isConnected = connected
    // );
  }
  ngOnDestroy(): void {
    throw new Error('Method not implemented.');
  }

  // private processMessageContent(content: string): ChatMessage {
  //   const htmlMatch = content.match(/```html\n([\s\S]*?)```/);
  //   if (htmlMatch) {
  //     const htmlContent = this.sanitizer.bypassSecurityTrustHtml(htmlMatch[1]);
  //     const textContent = content.replace(/```html\n[\s\S]*?```/, '').trim();
  //     return {
  //       role: 'assistant',
  //       content: textContent,
  //       htmlContent
  //     };
  //   }
  //   return { role: 'assistant', content };
  // }

  ngOnInit() {
  //   this.socketService.socket.on('chat_response_stream', ({ token }) => {
  //     this.isTyping = true;
  //     this.streamingResponse += token;
  //   });

  //   this.socketService.socket.on('chat_response_complete', ({ response, mcpResults }) => {
  //     this.isTyping = false;
  //     this.streamingResponse = '';
  //     const processedMessage = this.processMessageContent(response);
  //     this.messages.push({
  //       ...processedMessage,
  //       mcpResults
  //     });
  //   });

  //   this.socketService.socket.on('chat_error', ({ error }) => {
  //     console.error('Chat error:', error);
  //   });

  //   this.socketService.socket.on('chat_history', (history: ChatMessage[]) => {
  //     this.messages = history.map(msg => 
  //       msg.role === 'assistant' ? this.processMessageContent(msg.content) : msg
  //     );
  //   });

  //   this.socketService.socket.on('system_message', ({ timestamp, content }) => {
  //     this.messages.push({
  //       role: 'system',
  //       content: `[${new Date(timestamp).toLocaleTimeString()}] ${content}`
  //     });
  //   });

  //   this.socketService.socket.emit('get_chat_history');
  // }

  // sendMessage() {
  //   if (!this.currentMessage.trim()) return;

  //   this.messages.push({
  //     role: 'user',
  //     content: this.currentMessage
  //   });

  //   this.socketService.socket.emit('chat_message', this.currentMessage);
  //   this.currentMessage = '';
  // }

  // clearHistory() {
  //   this.socketService.socket.emit('clear_chat_history');
  //   this.messages = [];
  // }

  // ngOnDestroy() {
  //   if (this.subscription) {
  //     this.subscription.unsubscribe();
  //   }
  // }
  }
}
