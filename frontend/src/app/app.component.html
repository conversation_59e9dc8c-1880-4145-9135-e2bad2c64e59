<!-- <main class="min-h-screen bg-black bg-gradient-to-r from-[rgba(49,46,129,1)] to-[rgba(0,0,0,1)] p-4 font-mono">
    
</main> -->

<router-outlet></router-outlet>

<!-- 
<div class="border border-[rgba(49,46,129,0.3)] rounded p-2 mb-4 flex items-center space-x-2 bg-black/50 prose prose-invert max-w-none">
        <span class="w-2 h-2 rounded-full" [ngClass]="isConnected ? 'bg-green-500' : 'bg-red-500'"></span>
        <span class="text-gray-300 text-sm">{{ isConnected ? '> CONNECTION ESTABLISHED' : '> CONNECTION LOST' }}</span>
    </div>

    <div class="flex flex-col h-[calc(100vh-8rem)] from-[rgba(49,46,129,0.1)] to-[rgba(0,0,0,0.2)] bg-gradient-to-bl">
        <div class="flex-1 overflow-y-auto mb-4 space-y-2">
            <div *ngFor="let message of messages" 
                 class="rounded border border-[rgba(49,46,129,0.3)] p-2"
                 [ngClass]="{
                     'bg-[rgba(49,46,129,0.1)]': message.role === 'assistant',
                     'bg-[rgba(49,46,129,0.2)]': message.role === 'user',
                     'bg-[rgba(49,46,129,0.15)]': message.role === 'system'
                 }">
                <div class="text-sm text-gray-400 mb-1">
                    {{ message.role === 'assistant' ? '> AI:' : 
                       message.role === 'user' ? '> USER:' : '> SYSTEM:' }}
                </div>
                <div class="message-content whitespace-pre-wrap text-gray-300">{{ message.content }}</div>
                <div *ngIf="message.htmlContent" [innerHTML]="message.htmlContent"></div>
                <div *ngIf="message.mcpResults" class="mt-2 text-xs">
                    <pre class="bg-black/50 p-2 rounded">{{ message.mcpResults | json }}</pre>
                </div>
            </div>

            <div *ngIf="isTyping" class="animate-pulse">
                <div class="text-sm text-gray-400">> AI is typing...</div>
                <div class="text-gray-300">{{ streamingResponse }}</div>
            </div>
        </div>

        <div class="border-t border-[rgba(49,46,129,0.3)] pt-4 space-y-2">
            <div class="flex space-x-2">
                <input type="text" 
                       [(ngModel)]="currentMessage" 
                       (keyup.enter)="sendMessage()"
                       [disabled]="!isConnected"
                       class="flex-1 bg-black/50 border border-[rgba(49,46,129,0.3)] rounded px-3 py-2 focus:outline-none focus:border-[rgba(49,46,129,0.6)] text-gray-300"
                       placeholder="> Enter command...">
                
                <button (click)="sendMessage()" 
                        [disabled]="!isConnected || !currentMessage.trim()"
                        class="px-4 py-2 border border-[rgba(49,46,129,0.3)] rounded hover:bg-[rgba(49,46,129,0.2)] disabled:opacity-50 disabled:cursor-not-allowed text-gray-300">
                    Send
                </button>
                
                <button (click)="clearHistory()" 
                        [disabled]="!isConnected"
                        class="px-4 py-2 border border-[rgba(49,46,129,0.3)] rounded hover:bg-[rgba(49,46,129,0.2)] disabled:opacity-50 disabled:cursor-not-allowed text-gray-300">
                    Clear
                </button>
            </div>
        </div>
    </div> -->