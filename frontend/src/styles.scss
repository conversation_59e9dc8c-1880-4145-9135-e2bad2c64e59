@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.3); /* green-500 with opacity */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.5);
}

/* Terminal blinking cursor */
.cursor::after {
  content: '_';
  opacity: 0;
  animation: cursor 1s infinite;
}

@keyframes cursor {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* Ensure the app takes full height */
html, body {
  height: 100%;
  margin: 0;
  background-color: black;
  font-family: 'Courier New', Courier, monospace;
}
